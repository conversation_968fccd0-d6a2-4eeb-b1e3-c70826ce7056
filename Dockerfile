FROM iregistry.baidu-int.com/baidu-base/python:3.10-jammy

# Install system dependencies (excluding ffmpeg from apt)
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
    libgl1 libglib2.0-0 python3-pip wget xz-utils \
    && rm -rf /var/lib/apt/lists/*

# Install latest ffmpeg from official static builds
# This ensures we get a recent version that can handle modern video formats
RUN wget -q https://johnvansickle.com/ffmpeg/releases/ffmpeg-release-amd64-static.tar.xz \
    && tar -xf ffmpeg-release-amd64-static.tar.xz \
    && mv ffmpeg-*-amd64-static/ffmpeg /usr/local/bin/ \
    && mv ffmpeg-*-amd64-static/ffprobe /usr/local/bin/ \
    && rm -rf ffmpeg-* \
    && chmod +x /usr/local/bin/ffmpeg /usr/local/bin/ffprobe
RUN mkdir -p /home/<USER>/app && chown -R work:work /home/<USER>
WORKDIR /home/<USER>/app
USER work

ENV PATH="/home/<USER>/.local/bin:$PATH"
ENV PYTHONPATH=/home/<USER>/.local/lib/python3.10/site-packages

COPY --chown=work:work . .
RUN pip3 install --user --no-cache-dir -r ./requirements.txt \
    && pip3 install --user --no-cache-dir uvicorn \
    && mkdir -p tmp logs

# Start via uvicorn, picking up LISTEN_PORT if set
CMD ["/bin/sh", "-lc", "uvicorn app:app --workers ${workers:-1} --host 0.0.0.0 --timeout-keep-alive ${timeout:-60} --port ${main:-8080}"]