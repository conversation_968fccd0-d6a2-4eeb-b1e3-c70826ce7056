FROM iregistry.baidu-int.com/baidu-base/python:3.10-jammy

# 基础运行库（headless 场景，无 GUI）
RUN apt-get update \
 && apt-get install -y --no-install-recommends \
      libglib2.0-0 libstdc++6 ca-certificates curl tini \
 && rm -rf /var/lib/apt/lists/*

# venv
ENV VENV_PATH=/opt/venv
RUN python3 -m venv ${VENV_PATH} \
 && ${VENV_PATH}/bin/pip install --upgrade pip setuptools wheel
ENV PATH="${VENV_PATH}/bin:${PATH}"
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    OMP_NUM_THREADS=1 \
    MPLBACKEND=Agg

# 先仅拷 requirements 便于缓存
WORKDIR /home/<USER>/app
COPY --chown=work:work requirements.txt ./requirements.txt

# 安装运行依赖（requirements.txt 中应包含 moviepy 和 imageio-ffmpeg，
# 建议用 opencv-python-headless 而非 opencv-python）
RUN pip install --no-cache-dir -r requirements.txt

# 预热 imageio-ffmpeg，并在 PATH 中放置 ffmpeg/ffprobe wrapper
# 这样 moviepy/第三方若按 "ffmpeg" 调用也能透明使用 imageio-ffmpeg 自带二进制
RUN python - <<'PY'\n\
import os, stat, textwrap, imageio_ffmpeg\n\
# 触发下载并得到实际二进制路径（缓存到 ~/.imageio/ffmpeg/ 下）\n\
imageio_ffmpeg.get_ffmpeg_exe(); imageio_ffmpeg.get_ffprobe_exe()\n\
def write_wrapper(name, getter):\n\
    p = f'/usr/local/bin/{name}'\n\
    src = textwrap.dedent('''\n\
        #!/usr/bin/env python3\n\
        import os, sys, imageio_ffmpeg\n\
        exe = imageio_ffmpeg.%s()\n\
        os.execv(exe, [exe] + sys.argv[1:])\n\
    ''' % getter)\n\
    with open(p, 'w') as f:\n\
        f.write(src)\n\
    os.chmod(p, os.stat(p).st_mode | stat.S_IEXEC)\n\
write_wrapper('ffmpeg', 'get_ffmpeg_exe')\n\
write_wrapper('ffprobe', 'get_ffprobe_exe')\n\
print('ffmpeg/ffprobe wrappers installed')\n\
PY

# 拷贝项目代码
COPY --chown=work:work . .

# 运行用户
RUN useradd -m -u 1000 work || true && chown -R work:work /home/<USER>
USER work
WORKDIR /home/<USER>/app

# 简单的启动脚本（支持用环境变量改端口/并发/超时）
RUN printf '%s\n' '#!/usr/bin/env sh' \
  'set -e' \
  ': "${host:=0.0.0.0}"' \
  ': "${main:=8080}"' \
  ': "${workers:=8}"' \
  ': "${timeout:=60}"' \
  'exec uvicorn app:app --host "$host" --port "$main" --workers "$workers" --timeout-keep-alive "$timeout"' \
  > /usr/local/bin/start.sh \
 && chmod +x /usr/local/bin/start.sh

ENTRYPOINT ["/usr/bin/tini", "--"]
CMD ["/usr/local/bin/start.sh"]
