"""视频工具处理模块。

本模块提供视频处理工具的执行逻辑，包括裁剪、选择帧等操作。
本模块仅关注视频处理逻辑，不包含任何与HTTP请求相关的代码。
本模块的所有函数和类都应为静态函数和类，不包含任何实例状态。
"""

import os
import time
import tempfile
from typing import Dict, Any, Tuple

import cv2

from util.trace_util import get_trace_logger
from util.download_util import download_to_tmp_path
from util.upload_util import upload_file_to_bos

SUPPORTED_VIDEO_TOOLS = {
    "video_clip_tool",
    "video_select_frames_tool",
}


def _ensure_tmp() -> str:
    date_dir = time.strftime("%Y-%m-%d")
    tmp_visual_base = "./tmp"
    tmp_visual_dir = os.path.join(tmp_visual_base, date_dir)
    os.makedirs(tmp_visual_dir, exist_ok=True)
    return tmp_visual_dir


def apply_video_tool(
    name: str, args: Dict[str, Any], extra_params: Dict[str, Any]
) -> Tuple[str, Any]:
    """
    执行视频工具。
    """
    logger = get_trace_logger("visual_tool")

    video_url = extra_params.get("video_url")
    if not video_url:
        raise ValueError("extra_params.video_url is required for video tools")

    # download
    suffix = os.path.splitext(video_url.split("?")[0])[1] or ".mp4"
    logger.info("video_download_start")
    try:
        video_path = download_to_tmp_path(video_url, default_suffix=suffix)
    except Exception as e:
            logger.error("video_download_failed")
            raise ValueError(f"failed to download video: {e}")

    try:
        if name == "video_clip_tool":
            try:
                from moviepy import VideoFileClip
            except Exception as e:
                raise RuntimeError(
                    "moviepy VideoFileClip not available; install moviepy"
                ) from e

            start_time = args.get("start_time")
            end_time = args.get("end_time")
            if start_time is None or end_time is None:
                raise ValueError("start_time and end_time are required")

            tmp_dir = _ensure_tmp()
            fd, output_path = tempfile.mkstemp(
                suffix=suffix, prefix="_tmp_out_", dir=tmp_dir
            )
            os.close(fd)

            clip = VideoFileClip(video_path)
            try:
                duration = getattr(clip, "duration", None)
                logger.info(f"video duration: {duration}")
            except Exception as e:
                logger.error(f"getattr_duration_failed, error={str(e)}")

            sub = clip.subclipped(start_time, end_time)
            sub.write_videofile(output_path)

            # 上传
            size_kb = round(os.path.getsize(output_path) / 1024.0, 2)
            logger.info(f"upload_invoke")
            url = upload_file_to_bos(
                output_path, object_key_prefix="visual_tools/output"
            )
            logger.info("video_processing_done")
            try:
                os.unlink(output_path)
            except Exception as e:
                logger.error(f"tmp_cleanup_failed, path={output_path}, error={str(e)}")
            return ("video_url", url)

        elif name == "video_select_frames_tool":
            target_frames = args.get("target_frame")
            if not isinstance(target_frames, list) or not all(
                isinstance(i, int) for i in target_frames
            ):
                raise ValueError("target_frame must be a list of ints")
            urls = []
            cap = cv2.VideoCapture(video_path)
            frame_idx = 0
            tmp_dir = _ensure_tmp()
            logger.info("video_processing_start")
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                if frame_idx in target_frames:
                    frame_path = os.path.join(tmp_dir, f"_tmp_frame_{frame_idx}.png")
                    cv2.imwrite(frame_path, frame)
                    size_kb = round(os.path.getsize(frame_path) / 1024.0, 2)
                    logger.info(
                        f"upload_invoke: frame_idx={frame_idx}, size_kb={size_kb}, frame_path={frame_path}"
                    )
                    url = upload_file_to_bos(
                        frame_path, object_key_prefix="visual_tools/output"
                    )
                    urls.append(url)
                    try:
                        os.unlink(frame_path)
                        logger.info("tmp_cleanup_ok")
                    except Exception as e:
                        logger.error(
                            f"tmp_cleanup_failed, path={frame_path}, error={str(e)}"
                        )
                frame_idx += 1
            cap.release()
            logger.info("video_processing_done")
            return ("image_url_list", urls)

        else:
            raise ValueError(f"Unknown video tool: {name}")
    finally:
        try:
            os.unlink(video_path)
            logger.info("tmp_cleanup_ok")
        except Exception as e:
            logger.error(f"tmp_cleanup_failed, path={video_path}, error={str(e)}")
