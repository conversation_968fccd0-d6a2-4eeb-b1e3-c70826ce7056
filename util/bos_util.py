# Copyright (c) 2025 PaddlePaddle Authors. All Rights Reserved.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""BOS客户端工具模块。

本模块提供BOS（百度对象存储）客户端的线程安全初始化和管理功能。
"""

import threading
import logging
import baidubce
from baidubce.bce_client_configuration import BceClientConfiguration
from baidubce.auth.bce_credentials import BceCredentials
from baidubce.services.bos.bos_client import BosClient as BceBosClient

logger = logging.getLogger("visual_tool")

# bos配置信息
bos_client = None
bos_client_lock = threading.Lock()  # 线程锁，保证BOS客户端初始化的线程安全
ak = "ALTAKfgI1uR7BgxHKOrqHxauEN"
sk = "5f0f65c0b7974f0a89a4f2ae13e9c34d"
endpoint = "bj.bcebos.com"
bucket = "mcp-env"


def get_bos_client():
    """获取BOS客户端实例，支持延迟初始化和线程安全。

    使用双重检查锁定模式确保BOS（百度对象存储）客户端的线程安全单例初始化。

    返回:
        BceBosClient: 已初始化的BOS客户端实例。
    """
    global bos_client

    # 双重检查锁定
    if bos_client is None:
        with bos_client_lock:
            if bos_client is None:
                logger.info("Initializing BOS client...")
                cfg = BceClientConfiguration(
                    credentials=BceCredentials(ak, sk),
                    endpoint=endpoint,
                    protocol = baidubce.protocol.HTTPS

                )
                cfg.connection_timeout_in_mills = 10000
                cfg.recv_buf_size = 8 << 20 
                cfg.send_buf_size = 8 << 20
                bos_client = BceBosClient(cfg)
                logger.info("BOS client initialized successfully")

    return bos_client
