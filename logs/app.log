2025-08-29 16:46:44 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-08-29 16:46:44 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-08-29 16:46:44 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-08-29 16:59:23 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-08-29 16:59:23 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-08-29 16:59:23 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-08-29 20:19:17 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-08-29 20:19:17 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-08-29 20:19:17 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-08-29 20:19:17 - uvicorn.error - INFO - server - _serve:84 - Started server process [125]
2025-08-29 20:19:17 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-29 20:19:17 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-29 20:19:17 - uvicorn.error - INFO - server - _log_started_message:216 - Uvicorn running on http://0.0.0.0:8080 (Press CTRL+C to quit)
2025-08-29 20:19:30 - visual_tool - INFO - middleware - dispatch:113 - [TraceID: 1756469970125_a4ecb507] 请求开始: POST /visual_tools/v1/visual_tool
2025-08-29 20:19:30 - visual_tool - INFO - processor - apply_image_tool:73 - [TraceID: 1756469970125_a4ecb507] image_download_start
2025-08-29 20:19:30 - visual_tool - INFO - download_util - download_to_tmp_path:44 - [TraceID: 1756469970125_a4ecb507] download_start
2025-08-29 20:19:30 - visual_tool - INFO - download_util - download_to_tmp_path:53 - [TraceID: 1756469970125_a4ecb507] download_attempt
2025-08-29 20:19:30 - visual_tool - INFO - download_util - download_to_tmp_path:65 - [TraceID: 1756469970125_a4ecb507] download_success
2025-08-29 20:19:30 - visual_tool - INFO - processor - apply_image_tool:78 - [TraceID: 1756469970125_a4ecb507] image_download_success
2025-08-29 20:19:30 - visual_tool - INFO - processor - apply_image_tool:97 - [TraceID: 1756469970125_a4ecb507] image_processing_start
2025-08-29 20:19:30 - visual_tool - INFO - processor - apply_image_tool:226 - [TraceID: 1756469970125_a4ecb507] upload_invoke
2025-08-29 20:19:30 - visual_tool - INFO - upload_util - upload_file_to_bos:32 - [TraceID: 1756469970125_a4ecb507] upload_start
2025-08-29 20:19:30 - visual_tool - INFO - bos_util - get_bos_client:50 - Initializing BOS client...
2025-08-29 20:19:30 - visual_tool - INFO - bos_util - get_bos_client:59 - BOS client initialized successfully
2025-08-29 20:19:30 - visual_tool - INFO - upload_util - upload_file_to_bos:51 - [TraceID: 1756469970125_a4ecb507] upload_success
2025-08-29 20:19:30 - visual_tool - INFO - processor - apply_image_tool:228 - [TraceID: 1756469970125_a4ecb507] image_processing_done
2025-08-29 20:19:30 - visual_tool - INFO - trace_util - log_request_response:151 - [TraceID: 1756469970125_a4ecb507] 请求数据: {'name': 'image_calchist_tool', 'version': '', 'call_id': '', 'arguments': {'channels': [0]}, 'extra_params': {'image_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073'}}
2025-08-29 20:19:30 - visual_tool - INFO - trace_util - log_request_response:158 - [TraceID: 1756469970125_a4ecb507] 响应数据: {'code': 0, 'message': 'Finish', 'data': '{"type": "image_url", "image_url": "https://mcp-env.bj.bcebos.com/visual_tools/output/9e801c9852967c881d46e0290d348b17_1756469970583_96270.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-29T12%3A19%3A30Z%2F-1%2F%2Fe319f81dc3319210acbd6dfcc1c81873341638471fd45437aa7fe4385d7bae98"}'}
2025-08-29 20:19:30 - visual_tool - INFO - middleware - dispatch:133 - [TraceID: 1756469970125_a4ecb507] 请求完成: 200 - 耗时: 561ms
2025-08-29 20:20:09 - visual_tool - INFO - middleware - dispatch:113 - [TraceID: 1756470009949_c2fda02d] 请求开始: POST /visual_tools/v1/visual_tool
2025-08-29 20:20:09 - visual_tool - INFO - processor - apply_image_tool:73 - [TraceID: 1756470009949_c2fda02d] image_download_start
2025-08-29 20:20:09 - visual_tool - INFO - download_util - download_to_tmp_path:44 - [TraceID: 1756470009949_c2fda02d] download_start
2025-08-29 20:20:09 - visual_tool - INFO - download_util - download_to_tmp_path:53 - [TraceID: 1756470009949_c2fda02d] download_attempt
2025-08-29 20:20:10 - visual_tool - INFO - download_util - download_to_tmp_path:65 - [TraceID: 1756470009949_c2fda02d] download_success
2025-08-29 20:20:10 - visual_tool - INFO - processor - apply_image_tool:78 - [TraceID: 1756470009949_c2fda02d] image_download_success
2025-08-29 20:20:10 - visual_tool - INFO - processor - apply_image_tool:97 - [TraceID: 1756470009949_c2fda02d] image_processing_start
2025-08-29 20:20:10 - visual_tool - INFO - processor - apply_image_tool:226 - [TraceID: 1756470009949_c2fda02d] upload_invoke
2025-08-29 20:20:10 - visual_tool - INFO - upload_util - upload_file_to_bos:32 - [TraceID: 1756470009949_c2fda02d] upload_start
2025-08-29 20:20:10 - visual_tool - INFO - upload_util - upload_file_to_bos:51 - [TraceID: 1756470009949_c2fda02d] upload_success
2025-08-29 20:20:10 - visual_tool - INFO - processor - apply_image_tool:228 - [TraceID: 1756470009949_c2fda02d] image_processing_done
2025-08-29 20:20:10 - visual_tool - INFO - trace_util - log_request_response:151 - [TraceID: 1756470009949_c2fda02d] 请求数据: {'name': 'image_calchist_tool', 'version': '', 'call_id': '', 'arguments': {'channels': [0]}, 'extra_params': {'image_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073'}}
2025-08-29 20:20:10 - visual_tool - INFO - trace_util - log_request_response:158 - [TraceID: 1756470009949_c2fda02d] 响应数据: {'code': 0, 'message': 'Finish', 'data': '{"type": "image_url", "image_url": "https://mcp-env.bj.bcebos.com/visual_tools/output/9e801c9852967c881d46e0290d348b17_1756470010122_79757.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-29T12%3A20%3A10Z%2F-1%2F%2Fe0e4dd148bd3b7a183381cde96479d8fcf8a9ade0468b6aec71436e7beb4379b"}'}
2025-08-29 20:20:10 - visual_tool - INFO - middleware - dispatch:133 - [TraceID: 1756470009949_c2fda02d] 请求完成: 200 - 耗时: 307ms
2025-08-29 20:23:55 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-29 20:23:55 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-29 20:23:55 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-29 20:23:55 - uvicorn.error - INFO - server - _serve:94 - Finished server process [125]
2025-08-29 20:27:45 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-08-29 20:27:45 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-08-29 20:27:45 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-08-29 20:27:45 - uvicorn.error - INFO - server - _serve:84 - Started server process [2236]
2025-08-29 20:27:45 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-29 20:27:45 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-29 20:27:45 - uvicorn.error - INFO - server - _log_started_message:216 - Uvicorn running on http://0.0.0.0:8080 (Press CTRL+C to quit)
2025-08-29 20:27:48 - visual_tool - INFO - middleware - dispatch:113 - 请求开始: POST /visual_tools/v1/visual_tool
2025-08-29 20:27:48 - visual_tool - INFO - processor - apply_image_tool:73 - image_download_start
2025-08-29 20:27:48 - visual_tool - INFO - download_util - download_to_tmp_path:44 - download_start
2025-08-29 20:27:48 - visual_tool - INFO - download_util - download_to_tmp_path:53 - download_attempt
2025-08-29 20:27:49 - visual_tool - INFO - download_util - download_to_tmp_path:65 - download_success
2025-08-29 20:27:49 - visual_tool - INFO - processor - apply_image_tool:78 - image_download_success
2025-08-29 20:27:49 - visual_tool - INFO - processor - apply_image_tool:97 - image_processing_start
2025-08-29 20:27:49 - visual_tool - INFO - processor - apply_image_tool:226 - upload_invoke
2025-08-29 20:27:49 - visual_tool - INFO - upload_util - upload_file_to_bos:32 - upload_start
2025-08-29 20:27:49 - visual_tool - INFO - bos_util - get_bos_client:50 - Initializing BOS client...
2025-08-29 20:27:49 - visual_tool - INFO - bos_util - get_bos_client:59 - BOS client initialized successfully
2025-08-29 20:27:49 - visual_tool - INFO - upload_util - upload_file_to_bos:51 - upload_success
2025-08-29 20:27:49 - visual_tool - INFO - processor - apply_image_tool:228 - image_processing_done
2025-08-29 20:27:49 - visual_tool - INFO - trace_util - log_request_response:148 - 请求数据: {'name': 'image_calchist_tool', 'version': '', 'call_id': '', 'arguments': {'channels': [0]}, 'extra_params': {'image_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073'}}
2025-08-29 20:27:49 - visual_tool - INFO - trace_util - log_request_response:155 - 响应数据: {'code': 0, 'message': 'Finish', 'data': '{"type": "image_url", "image_url": "https://mcp-env.bj.bcebos.com/visual_tools/output/9e801c9852967c881d46e0290d348b17_1756470469517_56619.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-29T12%3A27%3A49Z%2F-1%2F%2Fc3df2326057f6574c9a591956ce5f081601f72f0b4be57bdad5397289cda7f9f"}'}
2025-08-29 20:27:49 - visual_tool - INFO - middleware - dispatch:133 - 请求完成: 200 - 耗时: 721ms
2025-08-29 20:28:38 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-29 20:28:38 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-29 20:28:38 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-29 20:28:38 - uvicorn.error - INFO - server - _serve:94 - Finished server process [2236]
2025-08-29 20:28:53 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-08-29 20:28:53 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-08-29 20:28:53 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-08-29 20:28:53 - uvicorn.error - INFO - server - _serve:84 - Started server process [3009]
2025-08-29 20:28:53 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-29 20:28:53 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-29 20:28:53 - uvicorn.error - INFO - server - _log_started_message:216 - Uvicorn running on http://0.0.0.0:8080 (Press CTRL+C to quit)
2025-08-29 20:28:56 - visual_tool - INFO - middleware - dispatch:113 - 请求开始: POST /visual_tools/v1/visual_tool
2025-08-29 20:28:56 - visual_tool - INFO - processor - apply_image_tool:73 - image_download_start
2025-08-29 20:28:56 - visual_tool - INFO - download_util - download_to_tmp_path:44 - download_start
2025-08-29 20:28:56 - visual_tool - INFO - download_util - download_to_tmp_path:53 - download_attempt
2025-08-29 20:28:56 - visual_tool - INFO - download_util - download_to_tmp_path:65 - download_success
2025-08-29 20:28:56 - visual_tool - INFO - processor - apply_image_tool:78 - image_download_success
2025-08-29 20:28:56 - visual_tool - INFO - processor - apply_image_tool:97 - image_processing_start
2025-08-29 20:28:56 - visual_tool - INFO - processor - apply_image_tool:226 - upload_invoke
2025-08-29 20:28:56 - visual_tool - INFO - upload_util - upload_file_to_bos:32 - upload_start
2025-08-29 20:28:56 - visual_tool - INFO - bos_util - get_bos_client:50 - Initializing BOS client...
2025-08-29 20:28:56 - visual_tool - INFO - bos_util - get_bos_client:59 - BOS client initialized successfully
2025-08-29 20:28:56 - visual_tool - INFO - upload_util - upload_file_to_bos:51 - upload_success
2025-08-29 20:28:56 - visual_tool - INFO - processor - apply_image_tool:228 - image_processing_done
2025-08-29 20:28:56 - visual_tool - INFO - trace_util - log_request_response:148 - 请求数据: {'name': 'image_calchist_tool', 'version': '', 'call_id': '', 'arguments': {'channels': [0]}, 'extra_params': {'image_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073'}}
2025-08-29 20:28:56 - visual_tool - INFO - trace_util - log_request_response:155 - 响应数据: {'code': 0, 'message': 'Finish', 'data': '{"type": "image_url", "image_url": "https://mcp-env.bj.bcebos.com/visual_tools/output/9e801c9852967c881d46e0290d348b17_1756470536795_83609.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-29T12%3A28%3A56Z%2F-1%2F%2Fd7f65bbaa8c50c1f6cbe49e0e6e946d937c1b4a909d0976877aa8a4ea1ad0c2a"}'}
2025-08-29 20:28:56 - visual_tool - INFO - middleware - dispatch:133 - 请求完成: 200 - 耗时: 408ms
2025-08-29 20:28:58 - visual_tool - INFO - middleware - dispatch:113 - 请求开始: POST /visual_tools/v1/visual_tool
2025-08-29 20:28:58 - visual_tool - INFO - processor - apply_image_tool:73 - image_download_start
2025-08-29 20:28:58 - visual_tool - INFO - download_util - download_to_tmp_path:44 - download_start
2025-08-29 20:28:58 - visual_tool - INFO - download_util - download_to_tmp_path:53 - download_attempt
2025-08-29 20:28:58 - visual_tool - INFO - download_util - download_to_tmp_path:65 - download_success
2025-08-29 20:28:58 - visual_tool - INFO - processor - apply_image_tool:78 - image_download_success
2025-08-29 20:28:58 - visual_tool - INFO - processor - apply_image_tool:97 - image_processing_start
2025-08-29 20:28:58 - visual_tool - INFO - processor - apply_image_tool:226 - upload_invoke
2025-08-29 20:28:58 - visual_tool - INFO - upload_util - upload_file_to_bos:32 - upload_start
2025-08-29 20:28:58 - visual_tool - INFO - upload_util - upload_file_to_bos:51 - upload_success
2025-08-29 20:28:58 - visual_tool - INFO - processor - apply_image_tool:228 - image_processing_done
2025-08-29 20:28:58 - visual_tool - INFO - trace_util - log_request_response:148 - 请求数据: {'name': 'image_calchist_tool', 'version': '', 'call_id': '', 'arguments': {'channels': [0]}, 'extra_params': {'image_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073'}}
2025-08-29 20:28:58 - visual_tool - INFO - trace_util - log_request_response:155 - 响应数据: {'code': 0, 'message': 'Finish', 'data': '{"type": "image_url", "image_url": "https://mcp-env.bj.bcebos.com/visual_tools/output/9e801c9852967c881d46e0290d348b17_1756470538274_54391.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-29T12%3A28%3A58Z%2F-1%2F%2Fa05f3262daf2c7a63523d1cccd4a4cda5b56622ca5d752139024eb799862956d"}'}
2025-08-29 20:28:58 - visual_tool - INFO - middleware - dispatch:133 - 请求完成: 200 - 耗时: 266ms
2025-08-29 20:31:11 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-29 20:31:12 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-29 20:31:12 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-29 20:31:12 - uvicorn.error - INFO - server - _serve:94 - Finished server process [3009]
2025-08-29 20:31:13 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-08-29 20:31:13 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-08-29 20:31:13 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-08-29 20:31:13 - uvicorn.error - INFO - server - _serve:84 - Started server process [3737]
2025-08-29 20:31:13 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-29 20:31:13 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-29 20:31:13 - uvicorn.error - INFO - server - _log_started_message:216 - Uvicorn running on http://0.0.0.0:8080 (Press CTRL+C to quit)
2025-08-29 20:31:15 - visual_tool - INFO - middleware - dispatch:113 - 请求开始: POST /visual_tools/v1/visual_tool
2025-08-29 20:31:15 - visual_tool - INFO - processor - apply_image_tool:73 - image_download_start
2025-08-29 20:31:15 - visual_tool - INFO - download_util - download_to_tmp_path:44 - download_start
2025-08-29 20:31:15 - visual_tool - INFO - download_util - download_to_tmp_path:53 - download_attempt
2025-08-29 20:31:15 - visual_tool - INFO - download_util - download_to_tmp_path:65 - download_success
2025-08-29 20:31:16 - visual_tool - INFO - processor - apply_image_tool:78 - image_download_success
2025-08-29 20:31:16 - visual_tool - INFO - processor - apply_image_tool:97 - image_processing_start
2025-08-29 20:31:16 - visual_tool - INFO - processor - apply_image_tool:226 - upload_invoke
2025-08-29 20:31:16 - visual_tool - INFO - upload_util - upload_file_to_bos:32 - upload_start
2025-08-29 20:31:16 - visual_tool - INFO - bos_util - get_bos_client:50 - Initializing BOS client...
2025-08-29 20:31:16 - visual_tool - INFO - bos_util - get_bos_client:59 - BOS client initialized successfully
2025-08-29 20:31:16 - visual_tool - INFO - upload_util - upload_file_to_bos:51 - upload_success
2025-08-29 20:31:16 - visual_tool - INFO - processor - apply_image_tool:228 - image_processing_done
2025-08-29 20:31:16 - visual_tool - INFO - trace_util - log_request_response:148 - 请求数据: {'name': 'image_calchist_tool', 'version': '', 'call_id': '', 'arguments': {'channels': [0]}, 'extra_params': {'image_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073'}}
2025-08-29 20:31:16 - visual_tool - INFO - trace_util - log_request_response:155 - 响应数据: {'code': 0, 'message': 'Finish', 'data': '{"type": "image_url", "image_url": "https://mcp-env.bj.bcebos.com/visual_tools/output/9e801c9852967c881d46e0290d348b17_1756470676222_97630.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-29T12%3A31%3A16Z%2F-1%2F%2Fe2686bd917c60f1780e48796d78d427b41bdb90cc736ac6ad8d24735f8c613b4"}'}
2025-08-29 20:31:16 - visual_tool - INFO - middleware - dispatch:133 - 请求完成: 200 - 耗时: 486ms
2025-08-29 20:31:21 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-29 20:31:21 - uvicorn.error - INFO - server - _serve:94 - Finished server process [3737]
2025-08-29 20:31:21 - uvicorn.error - ERROR - on - send:134 - Traceback (most recent call last):
  File "/Users/<USER>/.local/share/uv/python/cpython-3.10.18-macos-aarch64-none/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/Users/<USER>/.local/share/uv/python/cpython-3.10.18-macos-aarch64-none/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/Users/<USER>/.local/share/uv/python/cpython-3.10.18-macos-aarch64-none/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/Users/<USER>/.local/share/uv/python/cpython-3.10.18-macos-aarch64-none/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/Users/<USER>/.local/share/uv/python/cpython-3.10.18-macos-aarch64-none/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.10/site-packages/uvicorn/server.py", line 70, in serve
    with self.capture_signals():
  File "/Users/<USER>/.local/share/uv/python/cpython-3.10.18-macos-aarch64-none/lib/python3.10/contextlib.py", line 142, in __exit__
    next(self.gen)
  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.10/site-packages/uvicorn/server.py", line 331, in capture_signals
    signal.raise_signal(captured_signal)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.10/site-packages/starlette/routing.py", line 701, in lifespan
    await receive()
  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.10/site-packages/uvicorn/lifespan/on.py", line 137, in receive
    return await self.receive_queue.get()
  File "/Users/<USER>/.local/share/uv/python/cpython-3.10.18-macos-aarch64-none/lib/python3.10/asyncio/queues.py", line 159, in get
    await getter
asyncio.exceptions.CancelledError

2025-08-29 20:31:48 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-08-29 20:31:48 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-08-29 20:31:48 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-08-29 20:31:48 - uvicorn.error - INFO - server - _serve:84 - Started server process [4236]
2025-08-29 20:31:48 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-29 20:31:48 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-29 20:31:48 - uvicorn.error - INFO - server - _log_started_message:216 - Uvicorn running on http://0.0.0.0:8080 (Press CTRL+C to quit)
2025-08-29 20:31:51 - visual_tool - INFO - middleware - dispatch:113 - 请求开始: POST /visual_tools/v1/visual_tool
2025-08-29 20:31:51 - visual_tool - INFO - processor - apply_image_tool:73 - image_download_start
2025-08-29 20:31:51 - visual_tool - INFO - download_util - download_to_tmp_path:44 - download_start
2025-08-29 20:31:51 - visual_tool - INFO - download_util - download_to_tmp_path:53 - download_attempt
2025-08-29 20:31:52 - visual_tool - INFO - download_util - download_to_tmp_path:65 - download_success
2025-08-29 20:31:52 - visual_tool - INFO - processor - apply_image_tool:78 - image_download_success
2025-08-29 20:31:52 - visual_tool - INFO - processor - apply_image_tool:97 - image_processing_start
2025-08-29 20:31:52 - visual_tool - INFO - processor - apply_image_tool:226 - upload_invoke
2025-08-29 20:31:52 - visual_tool - INFO - upload_util - upload_file_to_bos:32 - upload_start
2025-08-29 20:31:52 - visual_tool - INFO - bos_util - get_bos_client:50 - Initializing BOS client...
2025-08-29 20:31:52 - visual_tool - INFO - bos_util - get_bos_client:59 - BOS client initialized successfully
2025-08-29 20:31:52 - visual_tool - INFO - upload_util - upload_file_to_bos:51 - upload_success
2025-08-29 20:31:52 - visual_tool - INFO - processor - apply_image_tool:228 - image_processing_done
2025-08-29 20:31:52 - visual_tool - INFO - trace_util - log_request_response:148 - 请求数据: {'name': 'image_calchist_tool', 'version': '', 'call_id': '', 'arguments': {'channels': [0]}, 'extra_params': {'image_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073'}}
2025-08-29 20:31:52 - visual_tool - INFO - trace_util - log_request_response:155 - 响应数据: {'code': 0, 'message': 'Finish', 'data': '{"type": "image_url", "image_url": "https://mcp-env.bj.bcebos.com/visual_tools/output/9e801c9852967c881d46e0290d348b17_1756470712214_56181.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-29T12%3A31%3A52Z%2F-1%2F%2F52c9532b4fb5b0129a9dd05d336ebc5c9b8241483618731adc3e840ce6791090"}'}
2025-08-29 20:31:52 - visual_tool - INFO - middleware - dispatch:133 - 请求完成: 200 - 耗时: 418ms
2025-08-29 20:32:06 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-29 20:32:06 - uvicorn.error - INFO - server - _serve:94 - Finished server process [4236]
2025-08-29 20:32:06 - uvicorn.error - ERROR - on - send:134 - Traceback (most recent call last):
  File "/Users/<USER>/.local/share/uv/python/cpython-3.10.18-macos-aarch64-none/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/Users/<USER>/.local/share/uv/python/cpython-3.10.18-macos-aarch64-none/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/Users/<USER>/.local/share/uv/python/cpython-3.10.18-macos-aarch64-none/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/Users/<USER>/.local/share/uv/python/cpython-3.10.18-macos-aarch64-none/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/Users/<USER>/.local/share/uv/python/cpython-3.10.18-macos-aarch64-none/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.10/site-packages/uvicorn/server.py", line 70, in serve
    with self.capture_signals():
  File "/Users/<USER>/.local/share/uv/python/cpython-3.10.18-macos-aarch64-none/lib/python3.10/contextlib.py", line 142, in __exit__
    next(self.gen)
  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.10/site-packages/uvicorn/server.py", line 331, in capture_signals
    signal.raise_signal(captured_signal)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.10/site-packages/starlette/routing.py", line 701, in lifespan
    await receive()
  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.10/site-packages/uvicorn/lifespan/on.py", line 137, in receive
    return await self.receive_queue.get()
  File "/Users/<USER>/.local/share/uv/python/cpython-3.10.18-macos-aarch64-none/lib/python3.10/asyncio/queues.py", line 159, in get
    await getter
asyncio.exceptions.CancelledError

{"time": "2025-08-29 20:32:07", "logger": "visual_tool", "level": "INFO", "message": "使用默认日志配置"}
{"time": "2025-08-29 20:32:09", "logger": "visual_tool", "level": "INFO", "message": "请求开始: POST /visual_tools/v1/visual_tool", "trace_id": "1756470729707_3367b94b"}
{"time": "2025-08-29 20:32:09", "logger": "visual_tool", "level": "INFO", "message": "image_download_start", "trace_id": "1756470729707_3367b94b", "url": "https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073"}
{"time": "2025-08-29 20:32:09", "logger": "visual_tool", "level": "INFO", "message": "download_start", "trace_id": "1756470729707_3367b94b", "url": "https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073", "suffix": ".jpeg"}
{"time": "2025-08-29 20:32:09", "logger": "visual_tool", "level": "INFO", "message": "download_attempt", "trace_id": "1756470729707_3367b94b", "url": "https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073", "attempt": 1, "connect_timeout": 0.5, "read_timeout": 5.0}
{"time": "2025-08-29 20:32:09", "logger": "visual_tool", "level": "INFO", "message": "download_success", "trace_id": "1756470729707_3367b94b", "url": "https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073", "path": "/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-29/_tmp_download_d925tfs_.jpeg", "size_kb": 31.93, "attempts": 1, "elapsed_sec": 0.192}
{"time": "2025-08-29 20:32:09", "logger": "visual_tool", "level": "INFO", "message": "image_download_success", "trace_id": "1756470729707_3367b94b", "url": "https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073", "path": "/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-29/_tmp_download_d925tfs_.jpeg", "size_kb": 31.93}
{"time": "2025-08-29 20:32:09", "logger": "visual_tool", "level": "INFO", "message": "image_processing_start", "trace_id": "1756470729707_3367b94b", "tool": "image_calchist_tool", "width": 554, "height": 554}
{"time": "2025-08-29 20:32:10", "logger": "visual_tool", "level": "INFO", "message": "upload_invoke", "trace_id": "1756470729707_3367b94b", "tool": "image_calchist_tool", "path": "/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-29/_tmp_q98bsfh.jpeg", "size_kb": 19.37}
{"time": "2025-08-29 20:32:10", "logger": "visual_tool", "level": "INFO", "message": "upload_start", "trace_id": "1756470729707_3367b94b", "file_path": "/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-29/_tmp_q98bsfh.jpeg", "size_kb": 19.37, "prefix": "visual_tools/output"}
{"time": "2025-08-29 20:32:10", "logger": "visual_tool", "level": "INFO", "message": "Initializing BOS client..."}
{"time": "2025-08-29 20:32:10", "logger": "visual_tool", "level": "INFO", "message": "BOS client initialized successfully"}
{"time": "2025-08-29 20:32:10", "logger": "visual_tool", "level": "INFO", "message": "upload_success", "trace_id": "1756470729707_3367b94b", "file_path": "/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-29/_tmp_q98bsfh.jpeg", "size_kb": 19.37, "object_key": "visual_tools/output/9e801c9852967c881d46e0290d348b17_1756470730084_21181.jpeg", "url": "https://mcp-env.bj.bcebos.com/visual_tools/output/9e801c9852967c881d46e0290d348b17_1756470730084_21181.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-29T12%3A32%3A10Z%2F-1%2F%2Fb0f704d84d33d47350137fe185c4c2f69a37f6987da570616e53e4d4d9a26305"}
{"time": "2025-08-29 20:32:10", "logger": "visual_tool", "level": "INFO", "message": "image_processing_done", "trace_id": "1756470729707_3367b94b", "tool": "image_calchist_tool", "return_data": false, "url": "https://mcp-env.bj.bcebos.com/visual_tools/output/9e801c9852967c881d46e0290d348b17_1756470730084_21181.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-29T12%3A32%3A10Z%2F-1%2F%2Fb0f704d84d33d47350137fe185c4c2f69a37f6987da570616e53e4d4d9a26305"}
{"time": "2025-08-29 20:32:10", "logger": "visual_tool", "level": "INFO", "message": "请求数据: {'name': 'image_calchist_tool', 'version': '', 'call_id': '', 'arguments': {'channels': [0]}, 'extra_params': {'image_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073'}}", "trace_id": "1756470729707_3367b94b"}
{"time": "2025-08-29 20:32:10", "logger": "visual_tool", "level": "INFO", "message": "响应数据: {'code': 0, 'message': 'Finish', 'data': '{\"type\": \"image_url\", \"image_url\": \"https://mcp-env.bj.bcebos.com/visual_tools/output/9e801c9852967c881d46e0290d348b17_1756470730084_21181.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-29T12%3A32%3A10Z%2F-1%2F%2Fb0f704d84d33d47350137fe185c4c2f69a37f6987da570616e53e4d4d9a26305\"}'}", "trace_id": "1756470729707_3367b94b"}
{"time": "2025-08-29 20:32:10", "logger": "visual_tool", "level": "INFO", "message": "请求完成: 200 - 耗时: 498ms", "trace_id": "1756470729707_3367b94b"}
{"time": "2025-08-29 20:32:10", "logger": "access", "level": "INFO", "message": "127.0.0.1 - \"POST /visual_tools/v1/visual_tool\" 200 - 498ms - \"iAPI/1.0.0 (http://iapi.baidu-int.com)\""}
2025-08-29 21:15:09 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-08-29 21:15:09 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-08-29 21:15:09 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-08-29 21:15:09 - uvicorn.error - INFO - server - _serve:84 - Started server process [15210]
2025-08-29 21:15:09 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-29 21:15:09 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-29 21:15:09 - uvicorn.error - INFO - server - _log_started_message:216 - Uvicorn running on http://0.0.0.0:8080 (Press CTRL+C to quit)
2025-08-29 21:15:12 - visual_tool - INFO - middleware - dispatch:113 - [TraceID: 1756473312082_b3633e91] 请求开始: POST /visual_tools/v1/visual_tool
2025-08-29 21:15:12 - visual_tool - INFO - processor - apply_image_tool:73 - [TraceID: 1756473312082_b3633e91] image_download_start
2025-08-29 21:15:12 - visual_tool - INFO - download_util - download_to_tmp_path:44 - [TraceID: 1756473312082_b3633e91] download_start
2025-08-29 21:15:12 - visual_tool - INFO - download_util - download_to_tmp_path:53 - [TraceID: 1756473312082_b3633e91] download_attempt
2025-08-29 21:15:12 - visual_tool - ERROR - download_util - download_to_tmp_path:75 - [TraceID: 1756473312082_b3633e91] download_attempt_failed
2025-08-29 21:15:12 - visual_tool - INFO - download_util - download_to_tmp_path:53 - [TraceID: 1756473312082_b3633e91] download_attempt
2025-08-29 21:15:13 - visual_tool - ERROR - download_util - download_to_tmp_path:75 - [TraceID: 1756473312082_b3633e91] download_attempt_failed
2025-08-29 21:15:13 - visual_tool - INFO - download_util - download_to_tmp_path:53 - [TraceID: 1756473312082_b3633e91] download_attempt
2025-08-29 21:15:14 - visual_tool - ERROR - download_util - download_to_tmp_path:75 - [TraceID: 1756473312082_b3633e91] download_attempt_failed
2025-08-29 21:15:14 - visual_tool - ERROR - download_util - download_to_tmp_path:85 - [TraceID: 1756473312082_b3633e91] download_failed
2025-08-29 21:15:14 - visual_tool - ERROR - processor - apply_image_tool:80 - [TraceID: 1756473312082_b3633e91] image_download_failed
2025-08-29 21:15:14 - visual_tool - INFO - trace_util - log_request_response:151 - [TraceID: 1756473312082_b3633e91] 请求数据: {'name': 'image_calchist_tool', 'version': '', 'call_id': '', 'arguments': {'channels': [0]}, 'extra_params': {'image_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073'}}
2025-08-29 21:15:14 - visual_tool - ERROR - trace_util - log_request_response:155 - [TraceID: 1756473312082_b3633e91] 请求处理失败: failed to download image_url: HTTPSConnectionPool(host='mcp-env.bj.bcebos.com', port=443): Max retries exceeded with url: /v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073 (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x110785f00>, 'Connection to mcp-env.bj.bcebos.com timed out. (connect timeout=0.5)'))
2025-08-29 21:15:14 - visual_tool - INFO - middleware - dispatch:133 - [TraceID: 1756473312082_b3633e91] 请求完成: 200 - 耗时: 2.2s
2025-08-30 09:38:24 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-30 09:38:25 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-30 09:38:25 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-30 09:38:25 - uvicorn.error - INFO - server - _serve:94 - Finished server process [15210]
2025-08-30 09:43:22 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-08-30 09:43:22 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-08-30 09:43:22 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-08-30 09:43:22 - uvicorn.error - INFO - server - _serve:84 - Started server process [59574]
2025-08-30 09:43:22 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-30 09:43:22 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-30 09:43:22 - uvicorn.error - INFO - server - _log_started_message:216 - Uvicorn running on http://0.0.0.0:8080 (Press CTRL+C to quit)
2025-08-30 09:49:22 - visual_tool - INFO - middleware - dispatch:113 - [TraceID: 1756518562340_14bac527] 请求开始: POST /visual_tools/v1/visual_tool
2025-08-30 09:49:22 - visual_tool - INFO - processor - apply_image_tool:73 - [TraceID: 1756518562340_14bac527] image_download_start
2025-08-30 09:49:22 - visual_tool - INFO - download_util - download_to_tmp_path:44 - [TraceID: 1756518562340_14bac527] download_start
2025-08-30 09:49:22 - visual_tool - INFO - download_util - download_to_tmp_path:53 - [TraceID: 1756518562340_14bac527] download_attempt
2025-08-30 09:49:22 - visual_tool - INFO - download_util - download_to_tmp_path:65 - [TraceID: 1756518562340_14bac527] download_success
2025-08-30 09:49:22 - visual_tool - INFO - processor - apply_image_tool:78 - [TraceID: 1756518562340_14bac527] image_download_success
2025-08-30 09:49:22 - visual_tool - INFO - processor - apply_image_tool:97 - [TraceID: 1756518562340_14bac527] image_processing_start
2025-08-30 09:49:23 - visual_tool - INFO - processor - apply_image_tool:226 - [TraceID: 1756518562340_14bac527] upload_invoke
2025-08-30 09:49:23 - visual_tool - INFO - upload_util - upload_file_to_bos:32 - [TraceID: 1756518562340_14bac527] upload_start
2025-08-30 09:49:23 - visual_tool - INFO - bos_util - get_bos_client:50 - Initializing BOS client...
2025-08-30 09:49:23 - visual_tool - INFO - bos_util - get_bos_client:59 - BOS client initialized successfully
2025-08-30 09:49:23 - visual_tool - INFO - upload_util - upload_file_to_bos:51 - [TraceID: 1756518562340_14bac527] upload_success
2025-08-30 09:49:23 - visual_tool - INFO - processor - apply_image_tool:228 - [TraceID: 1756518562340_14bac527] image_processing_done
2025-08-30 09:49:23 - visual_tool - INFO - trace_util - log_request_response:151 - [TraceID: 1756518562340_14bac527] 请求数据: {'name': 'image_calchist_tool', 'version': '', 'call_id': '', 'arguments': {'channels': [0]}, 'extra_params': {'image_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073'}}
2025-08-30 09:49:23 - visual_tool - INFO - trace_util - log_request_response:158 - [TraceID: 1756518562340_14bac527] 响应数据: {'code': 0, 'message': 'Finish', 'data': '{"type": "image_url", "image_url": "https://mcp-env.bj.bcebos.com/visual_tools/output/9e801c9852967c881d46e0290d348b17_1756518563510_55416.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T01%3A49%3A23Z%2F-1%2F%2F71dab2108ca363b56a4d15d44e16594b67db04fa215e9fd435cbb7b6d313ac52"}'}
2025-08-30 09:49:23 - visual_tool - INFO - middleware - dispatch:133 - [TraceID: 1756518562340_14bac527] 请求完成: 200 - 耗时: 1.3s
2025-08-30 09:49:45 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-30 09:49:45 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-30 09:49:45 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-30 09:49:45 - uvicorn.error - INFO - server - _serve:94 - Finished server process [59574]
2025-08-30 09:50:00 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-08-30 09:50:00 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-08-30 09:50:00 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-08-30 09:50:00 - uvicorn.error - INFO - server - _serve:84 - Started server process [61092]
2025-08-30 09:50:00 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-30 09:50:00 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-30 09:50:00 - uvicorn.error - INFO - server - _log_started_message:216 - Uvicorn running on http://0.0.0.0:8080 (Press CTRL+C to quit)
2025-08-30 09:50:03 - visual_tool - INFO - middleware - dispatch:113 - [TraceID: 1756518603339_08354a03] 请求开始: POST /visual_tools/v1/visual_tool
2025-08-30 09:50:03 - visual_tool - INFO - download_util - download_to_tmp_path:44 - [TraceID: 1756518603339_08354a03] download_start
2025-08-30 09:50:03 - visual_tool - INFO - download_util - download_to_tmp_path:53 - [TraceID: 1756518603339_08354a03] download_attempt
2025-08-30 09:50:03 - visual_tool - INFO - download_util - download_to_tmp_path:65 - [TraceID: 1756518603339_08354a03] download_success
2025-08-30 09:50:03 - visual_tool - INFO - processor - apply_image_tool:78 - [TraceID: 1756518603339_08354a03] image_download_success
2025-08-30 09:50:03 - visual_tool - INFO - processor - apply_image_tool:97 - [TraceID: 1756518603339_08354a03] image_processing_start
2025-08-30 09:50:03 - visual_tool - INFO - processor - apply_image_tool:226 - [TraceID: 1756518603339_08354a03] upload_invoke
2025-08-30 09:50:03 - visual_tool - INFO - upload_util - upload_file_to_bos:32 - [TraceID: 1756518603339_08354a03] upload_start
2025-08-30 09:50:03 - visual_tool - INFO - bos_util - get_bos_client:50 - Initializing BOS client...
2025-08-30 09:50:03 - visual_tool - INFO - bos_util - get_bos_client:59 - BOS client initialized successfully
2025-08-30 09:50:03 - visual_tool - INFO - upload_util - upload_file_to_bos:51 - [TraceID: 1756518603339_08354a03] upload_success
2025-08-30 09:50:03 - visual_tool - INFO - processor - apply_image_tool:228 - [TraceID: 1756518603339_08354a03] image_processing_done
2025-08-30 09:50:03 - visual_tool - INFO - trace_util - log_request_response:151 - [TraceID: 1756518603339_08354a03] 请求数据: {'name': 'image_calchist_tool', 'version': '', 'call_id': '', 'arguments': {'channels': [0]}, 'extra_params': {'image_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073'}}
2025-08-30 09:50:03 - visual_tool - INFO - trace_util - log_request_response:158 - [TraceID: 1756518603339_08354a03] 响应数据: {'code': 0, 'message': 'Finish', 'data': '{"type": "image_url", "image_url": "https://mcp-env.bj.bcebos.com/visual_tools/output/9e801c9852967c881d46e0290d348b17_1756518603768_35805.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T01%3A50%3A03Z%2F-1%2F%2Fd85bfcdcf6455ea11bbc07fbe05040a8f6b8bcf5459a2666fc74a54db7edd4db"}'}
2025-08-30 09:50:03 - visual_tool - INFO - middleware - dispatch:133 - [TraceID: 1756518603339_08354a03] 请求完成: 200 - 耗时: 544ms
2025-08-30 09:55:22 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-08-30 09:55:22 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-08-30 09:55:22 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-08-30 09:55:22 - visual_tool - INFO - download_util - download_to_tmp_path:44 - download_start: url=https://httpbin.org/json, path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp_download__n3rrk0u.json, suffix=.json
2025-08-30 09:55:22 - visual_tool - INFO - download_util - download_to_tmp_path:50 - download_attempt: attempt=1, connect_timeout=0.5, read_timeout=5.0
2025-08-30 09:55:23 - visual_tool - INFO - download_util - download_to_tmp_path:57 - download_success: url=https://httpbin.org/json, path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp_download__n3rrk0u.json, size_kb=0.42, attempts=1, elapsed_sec=1.19
2025-08-30 09:55:23 - visual_tool - INFO - download_util - download_to_tmp_path:44 - download_start: url=https://invalid-url-that-does-not-exist.com/test.jpg, path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp_download_zrqpqs04.jpg, suffix=.jpg
2025-08-30 09:55:23 - visual_tool - INFO - download_util - download_to_tmp_path:50 - download_attempt: attempt=1, connect_timeout=0.5, read_timeout=5.0
2025-08-30 09:55:24 - visual_tool - ERROR - download_util - download_to_tmp_path:61 - download_attempt_failed: url=https://invalid-url-that-does-not-exist.com/test.jpg, attempt=1, error=HTTPSConnectionPool(host='invalid-url-that-does-not-exist.com', port=443): Max retries exceeded with url: /test.jpg (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x101781700>, 'Connection to invalid-url-that-does-not-exist.com timed out. (connect timeout=0.5)'))
2025-08-30 09:55:24 - visual_tool - INFO - download_util - download_to_tmp_path:50 - download_attempt: attempt=2, connect_timeout=0.5, read_timeout=5.0
2025-08-30 09:55:25 - visual_tool - INFO - download_util - download_to_tmp_path:57 - download_success: url=https://invalid-url-that-does-not-exist.com/test.jpg, path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp_download_zrqpqs04.jpg, size_kb=177.68, attempts=2, elapsed_sec=2.244
2025-08-30 09:55:55 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-08-30 09:55:55 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-08-30 09:55:55 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-08-30 09:55:55 - visual_tool - INFO - download_util - download_to_tmp_path:44 - download_start: url=https://httpbin.org/json, path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp_download_zp55e2bc.json, suffix=.json
2025-08-30 09:55:55 - visual_tool - INFO - download_util - download_to_tmp_path:50 - download_attempt: url=https://httpbin.org/json, attempt=1, connect_timeout=0.5, read_timeout=5.0
2025-08-30 09:55:56 - visual_tool - INFO - download_util - download_to_tmp_path:57 - download_success: size_kb=0.42, attempts=1, elapsed_sec=1.207
2025-08-30 09:55:56 - visual_tool - INFO - download_util - download_to_tmp_path:44 - download_start: url=https://invalid-url-that-does-not-exist.com/test.jpg, path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp_download_4977rsjo.jpg, suffix=.jpg
2025-08-30 09:55:56 - visual_tool - INFO - download_util - download_to_tmp_path:50 - download_attempt: url=https://invalid-url-that-does-not-exist.com/test.jpg, attempt=1, connect_timeout=0.5, read_timeout=5.0
2025-08-30 09:55:57 - visual_tool - INFO - download_util - download_to_tmp_path:57 - download_success: size_kb=148.39, attempts=1, elapsed_sec=1.042
2025-08-30 09:56:19 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-08-30 09:56:19 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-08-30 09:56:19 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-08-30 09:56:19 - visual_tool - INFO - download_util - download_to_tmp_path:44 - download_start: url=https://httpbin.org/json, path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp_download_une4sczx.json, suffix=.json
2025-08-30 09:56:19 - visual_tool - INFO - download_util - download_to_tmp_path:50 - download_attempt: url=https://httpbin.org/json, attempt=1, connect_timeout=0.5, read_timeout=5.0
2025-08-30 09:56:20 - visual_tool - INFO - download_util - download_to_tmp_path:57 - download_success: size_kb=0.42, attempts=1, elapsed_sec=0.923
2025-08-30 09:56:20 - visual_tool - INFO - download_util - download_to_tmp_path:44 - download_start: url=https://httpbin.org/status/404, path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp_download__79d_q9p.jpg, suffix=.jpg
2025-08-30 09:56:20 - visual_tool - INFO - download_util - download_to_tmp_path:50 - download_attempt: url=https://httpbin.org/status/404, attempt=1, connect_timeout=0.5, read_timeout=5.0
2025-08-30 09:56:21 - visual_tool - ERROR - download_util - download_to_tmp_path:61 - download_attempt_failed: attempt=1, error=404 Client Error: NOT FOUND for url: https://httpbin.org/status/404
2025-08-30 09:56:21 - visual_tool - INFO - download_util - download_to_tmp_path:50 - download_attempt: url=https://httpbin.org/status/404, attempt=2, connect_timeout=0.5, read_timeout=5.0
2025-08-30 09:56:23 - visual_tool - ERROR - download_util - download_to_tmp_path:61 - download_attempt_failed: attempt=2, error=404 Client Error: NOT FOUND for url: https://httpbin.org/status/404
2025-08-30 09:56:23 - visual_tool - INFO - download_util - download_to_tmp_path:50 - download_attempt: url=https://httpbin.org/status/404, attempt=3, connect_timeout=0.5, read_timeout=5.0
2025-08-30 09:56:25 - visual_tool - ERROR - download_util - download_to_tmp_path:61 - download_attempt_failed: attempt=3, error=404 Client Error: NOT FOUND for url: https://httpbin.org/status/404
2025-08-30 09:56:25 - visual_tool - ERROR - download_util - download_to_tmp_path:67 - download_failed: attempts=3, error=404 Client Error: NOT FOUND for url: https://httpbin.org/status/404
Traceback (most recent call last):
  File "/Users/<USER>/code/baidu/dataeng/visual_tools/util/download_util.py", line 52, in download_to_tmp_path
    resp.raise_for_status()
  File "/Users/<USER>/.local/share/mise/installs/python/3.12.11/lib/python3.12/site-packages/requests/models.py", line 1021, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 404 Client Error: NOT FOUND for url: https://httpbin.org/status/404
2025-08-30 09:57:47 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-08-30 09:57:47 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-08-30 09:57:47 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-08-30 09:57:47 - uvicorn.error - INFO - server - _serve:84 - Started server process [63495]
2025-08-30 09:57:47 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-30 09:57:47 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-30 09:57:47 - uvicorn.error - INFO - server - _log_started_message:216 - Uvicorn running on http://0.0.0.0:8080 (Press CTRL+C to quit)
2025-08-30 09:57:51 - visual_tool - INFO - middleware - dispatch:113 - [TraceID: 1756519071063_7db51502] 请求开始: POST /visual_tools/v1/visual_tool
2025-08-30 09:57:51 - visual_tool - INFO - processor - apply_image_tool:73 - [TraceID: 1756519071063_7db51502] image_download_start
2025-08-30 09:57:51 - visual_tool - INFO - download_util - download_to_tmp_path:44 - [TraceID: 1756519071063_7db51502] download_start: url=https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073, path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp_download_qdtvkkw3.jpeg, suffix=.jpeg
2025-08-30 09:57:51 - visual_tool - INFO - download_util - download_to_tmp_path:50 - [TraceID: 1756519071063_7db51502] download_attempt: attempt=1, connect_timeout=0.5, read_timeout=5.0
2025-08-30 09:57:51 - visual_tool - INFO - download_util - download_to_tmp_path:57 - [TraceID: 1756519071063_7db51502] download_success: size_kb=31.93, attempts=1, elapsed_sec=0.14
2025-08-30 09:57:51 - visual_tool - INFO - processor - apply_image_tool:78 - [TraceID: 1756519071063_7db51502] image_download_success
2025-08-30 09:57:51 - visual_tool - INFO - processor - apply_image_tool:97 - [TraceID: 1756519071063_7db51502] image_processing_start
2025-08-30 09:57:51 - visual_tool - INFO - processor - apply_image_tool:226 - [TraceID: 1756519071063_7db51502] upload_invoke
2025-08-30 09:57:51 - visual_tool - INFO - upload_util - upload_file_to_bos:32 - [TraceID: 1756519071063_7db51502] upload_start
2025-08-30 09:57:51 - visual_tool - INFO - bos_util - get_bos_client:50 - Initializing BOS client...
2025-08-30 09:57:51 - visual_tool - INFO - bos_util - get_bos_client:59 - BOS client initialized successfully
2025-08-30 09:57:51 - visual_tool - INFO - upload_util - upload_file_to_bos:51 - [TraceID: 1756519071063_7db51502] upload_success
2025-08-30 09:57:51 - visual_tool - INFO - processor - apply_image_tool:228 - [TraceID: 1756519071063_7db51502] image_processing_done
2025-08-30 09:57:51 - visual_tool - INFO - trace_util - log_request_response:151 - [TraceID: 1756519071063_7db51502] 请求数据: {'name': 'image_calchist_tool', 'version': '', 'call_id': '', 'arguments': {'channels': [0]}, 'extra_params': {'image_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073'}}
2025-08-30 09:57:51 - visual_tool - INFO - trace_util - log_request_response:158 - [TraceID: 1756519071063_7db51502] 响应数据: {'code': 0, 'message': 'Finish', 'data': '{"type": "image_url", "image_url": "https://mcp-env.bj.bcebos.com/visual_tools/output/9e801c9852967c881d46e0290d348b17_1756519071363_68067.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T01%3A57%3A51Z%2F-1%2F%2F30df965791c30f5247f697a186fe8acdac95c0d728191ec8192ef56e8737eb15"}'}
2025-08-30 09:57:51 - visual_tool - INFO - middleware - dispatch:133 - [TraceID: 1756519071063_7db51502] 请求完成: 200 - 耗时: 433ms
2025-08-30 10:05:18 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-30 10:05:18 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-30 10:05:18 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-30 10:05:18 - uvicorn.error - INFO - server - _serve:94 - Finished server process [63495]
2025-08-30 10:05:27 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-08-30 10:05:27 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-08-30 10:05:27 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-08-30 10:05:27 - uvicorn.error - INFO - server - _serve:84 - Started server process [66222]
2025-08-30 10:05:27 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-30 10:05:27 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-30 10:05:27 - uvicorn.error - INFO - server - _log_started_message:216 - Uvicorn running on http://0.0.0.0:8080 (Press CTRL+C to quit)
2025-08-30 10:05:30 - visual_tool - INFO - middleware - dispatch:113 - [TraceID: 1756519530412_08f1bf86] 请求开始: POST /visual_tools/v1/visual_tool
2025-08-30 10:05:30 - visual_tool - INFO - processor - apply_image_tool:77 - [TraceID: 1756519530412_08f1bf86] image_download_start
2025-08-30 10:05:30 - visual_tool - INFO - download_util - download_to_tmp_path:44 - [TraceID: 1756519530412_08f1bf86] download_start: url=https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073, path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp_download_mafiaugo.jpeg, suffix=.jpeg
2025-08-30 10:05:30 - visual_tool - INFO - download_util - download_to_tmp_path:50 - [TraceID: 1756519530412_08f1bf86] download_attempt: attempt=1, connect_timeout=0.5, read_timeout=5.0
2025-08-30 10:05:30 - visual_tool - INFO - download_util - download_to_tmp_path:57 - [TraceID: 1756519530412_08f1bf86] download_success: size_kb=31.93, attempts=1, elapsed_sec=0.175
2025-08-30 10:05:30 - visual_tool - INFO - processor - apply_image_tool:81 - [TraceID: 1756519530412_08f1bf86] image_download_success
2025-08-30 10:05:30 - visual_tool - INFO - processor - apply_image_tool:102 - [TraceID: 1756519530412_08f1bf86] image_processing_start: tool={name}, width= {w}, height= {h}
2025-08-30 10:05:30 - visual_tool - INFO - processor - apply_image_tool:250 - [TraceID: 1756519530412_08f1bf86] upload_invoke
2025-08-30 10:05:30 - visual_tool - INFO - upload_util - upload_file_to_bos:30 - [TraceID: 1756519530412_08f1bf86] upload_start
2025-08-30 10:05:30 - visual_tool - INFO - bos_util - get_bos_client:50 - Initializing BOS client...
2025-08-30 10:05:30 - visual_tool - INFO - bos_util - get_bos_client:59 - BOS client initialized successfully
2025-08-30 10:05:30 - visual_tool - INFO - upload_util - upload_file_to_bos:49 - [TraceID: 1756519530412_08f1bf86] upload_success
2025-08-30 10:05:30 - visual_tool - INFO - processor - apply_image_tool:261 - [TraceID: 1756519530412_08f1bf86] image_processing_done
2025-08-30 10:05:30 - visual_tool - INFO - trace_util - log_request_response:151 - [TraceID: 1756519530412_08f1bf86] 请求数据: {'name': 'image_calchist_tool', 'version': '', 'call_id': '', 'arguments': {'channels': [0]}, 'extra_params': {'image_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073'}}
2025-08-30 10:05:30 - visual_tool - INFO - trace_util - log_request_response:158 - [TraceID: 1756519530412_08f1bf86] 响应数据: {'code': 0, 'message': 'Finish', 'data': '{"type": "image_url", "image_url": "https://mcp-env.bj.bcebos.com/visual_tools/output/9e801c9852967c881d46e0290d348b17_1756519530742_45579.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T02%3A05%3A30Z%2F-1%2F%2F4141fdd87933c981358495b81ee8c6caf9578f0e981c45408196b2b016b28950"}'}
2025-08-30 10:05:30 - visual_tool - INFO - middleware - dispatch:133 - [TraceID: 1756519530412_08f1bf86] 请求完成: 200 - 耗时: 464ms
2025-08-30 10:19:49 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-30 10:19:49 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-30 10:19:49 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-30 10:19:49 - uvicorn.error - INFO - server - _serve:94 - Finished server process [66222]
2025-08-30 10:19:53 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-08-30 10:19:53 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-08-30 10:19:53 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-08-30 10:19:53 - uvicorn.error - INFO - server - _serve:84 - Started server process [68419]
2025-08-30 10:19:53 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-30 10:19:53 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-30 10:19:53 - uvicorn.error - INFO - server - _log_started_message:216 - Uvicorn running on http://0.0.0.0:8080 (Press CTRL+C to quit)
2025-08-30 10:19:56 - visual_tool - INFO - middleware - dispatch:113 - [TraceID: 1756520396105_7333108e] 请求开始: POST /visual_tools/v1/visual_tool
2025-08-30 10:19:56 - visual_tool - INFO - processor - apply_image_tool:77 - [TraceID: 1756520396105_7333108e] image_download_start
2025-08-30 10:19:56 - visual_tool - INFO - download_util - download_to_tmp_path:44 - [TraceID: 1756520396105_7333108e] download_start: url=https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073, path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp_download_jkpbbe3l.jpeg, suffix=.jpeg
2025-08-30 10:19:56 - visual_tool - INFO - download_util - download_to_tmp_path:56 - [TraceID: 1756520396105_7333108e] download_success: size_kb=31.93, attempts=1, elapsed_sec=0.158
2025-08-30 10:19:56 - visual_tool - INFO - processor - apply_image_tool:81 - [TraceID: 1756520396105_7333108e] image_download_success
2025-08-30 10:19:56 - visual_tool - INFO - processor - apply_image_tool:102 - [TraceID: 1756520396105_7333108e] image_processing_start: tool=image_calchist_tool, width= 554, height= 554
2025-08-30 10:19:56 - visual_tool - INFO - processor - apply_image_tool:243 - [TraceID: 1756520396105_7333108e] upload_invoke
2025-08-30 10:19:56 - visual_tool - INFO - upload_util - upload_file_to_bos:37 - [TraceID: 1756520396105_7333108e] upload_start: file_path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmpyri25yys.jpeg, object_key=visual_tools/output/9e801c9852967c881d46e0290d348b17_1756520396401_34370.jpeg, size_kb=19.37
2025-08-30 10:19:56 - visual_tool - INFO - bos_util - get_bos_client:50 - Initializing BOS client...
2025-08-30 10:19:56 - visual_tool - INFO - bos_util - get_bos_client:59 - BOS client initialized successfully
2025-08-30 10:19:56 - visual_tool - INFO - upload_util - upload_file_to_bos:43 - [TraceID: 1756520396105_7333108e] upload_success
2025-08-30 10:19:56 - visual_tool - INFO - processor - apply_image_tool:247 - [TraceID: 1756520396105_7333108e] image_processing_done
2025-08-30 10:19:56 - visual_tool - INFO - trace_util - log_request_response:151 - [TraceID: 1756520396105_7333108e] 请求数据: {'name': 'image_calchist_tool', 'version': '', 'call_id': '', 'arguments': {'channels': [0]}, 'extra_params': {'image_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073'}}
2025-08-30 10:19:56 - visual_tool - INFO - trace_util - log_request_response:158 - [TraceID: 1756520396105_7333108e] 响应数据: {'code': 0, 'message': 'Finish', 'data': '{"type": "image_url", "image_url": "https://mcp-env.bj.bcebos.com/visual_tools/output/9e801c9852967c881d46e0290d348b17_1756520396401_34370.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T02%3A19%3A56Z%2F-1%2F%2F2eda7b0d26c4dbc8cb18e2bb0219f55aefea5546fe03f8bffdcec2edbd63d13f"}'}
2025-08-30 10:19:56 - visual_tool - INFO - middleware - dispatch:133 - [TraceID: 1756520396105_7333108e] 请求完成: 200 - 耗时: 433ms
2025-08-30 10:20:00 - visual_tool - INFO - middleware - dispatch:113 - [TraceID: 1756520400870_49054457] 请求开始: POST /visual_tools/v1/visual_tool
2025-08-30 10:20:00 - visual_tool - INFO - processor - apply_image_tool:77 - [TraceID: 1756520400870_49054457] image_download_start
2025-08-30 10:20:00 - visual_tool - INFO - download_util - download_to_tmp_path:44 - [TraceID: 1756520400870_49054457] download_start: url=https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073, path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp_download_p09q65t3.jpeg, suffix=.jpeg
2025-08-30 10:20:01 - visual_tool - INFO - download_util - download_to_tmp_path:56 - [TraceID: 1756520400870_49054457] download_success: size_kb=31.93, attempts=1, elapsed_sec=0.159
2025-08-30 10:20:01 - visual_tool - INFO - processor - apply_image_tool:81 - [TraceID: 1756520400870_49054457] image_download_success
2025-08-30 10:20:01 - visual_tool - INFO - processor - apply_image_tool:102 - [TraceID: 1756520400870_49054457] image_processing_start: tool=image_calchist_tool, width= 554, height= 554
2025-08-30 10:20:01 - visual_tool - INFO - processor - apply_image_tool:243 - [TraceID: 1756520400870_49054457] upload_invoke
2025-08-30 10:20:01 - visual_tool - INFO - upload_util - upload_file_to_bos:37 - [TraceID: 1756520400870_49054457] upload_start: file_path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp6wgfxc9n.jpeg, object_key=visual_tools/output/9e801c9852967c881d46e0290d348b17_1756520401080_93892.jpeg, size_kb=19.37
2025-08-30 10:20:01 - visual_tool - INFO - upload_util - upload_file_to_bos:43 - [TraceID: 1756520400870_49054457] upload_success
2025-08-30 10:20:01 - visual_tool - INFO - processor - apply_image_tool:247 - [TraceID: 1756520400870_49054457] image_processing_done
2025-08-30 10:20:01 - visual_tool - INFO - trace_util - log_request_response:151 - [TraceID: 1756520400870_49054457] 请求数据: {'name': 'image_calchist_tool', 'version': '', 'call_id': '', 'arguments': {'channels': [0]}, 'extra_params': {'image_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073'}}
2025-08-30 10:20:01 - visual_tool - INFO - trace_util - log_request_response:158 - [TraceID: 1756520400870_49054457] 响应数据: {'code': 0, 'message': 'Finish', 'data': '{"type": "image_url", "image_url": "https://mcp-env.bj.bcebos.com/visual_tools/output/9e801c9852967c881d46e0290d348b17_1756520401080_93892.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T02%3A20%3A01Z%2F-1%2F%2F1aab23db6b8ad164afb9f2df2e8fb3c3e8968734e026b69c4653a996b78db48a"}'}
2025-08-30 10:20:01 - visual_tool - INFO - middleware - dispatch:133 - [TraceID: 1756520400870_49054457] 请求完成: 200 - 耗时: 465ms
2025-08-30 10:20:50 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-30 10:20:50 - uvicorn.error - INFO - server - _serve:94 - Finished server process [68419]
2025-08-30 10:20:50 - uvicorn.error - ERROR - on - send:134 - Traceback (most recent call last):
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/asyncio/base_events.py", line 641, in run_until_complete
    self.run_forever()
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/asyncio/base_events.py", line 608, in run_forever
    self._run_once()
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/asyncio/base_events.py", line 1936, in _run_once
    handle._run()
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/asyncio/events.py", line 84, in _run
    self._context.run(self._callback, *self._args)
  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 70, in serve
    with self.capture_signals():
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/contextlib.py", line 144, in __exit__
    next(self.gen)
  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 331, in capture_signals
    signal.raise_signal(captured_signal)
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/asyncio/runners.py", line 157, in _on_sigint
    raise KeyboardInterrupt()
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/starlette/routing.py", line 701, in lifespan
    await receive()
  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/uvicorn/lifespan/on.py", line 137, in receive
    return await self.receive_queue.get()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/asyncio/queues.py", line 158, in get
    await getter
asyncio.exceptions.CancelledError

2025-08-30 12:31:01 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-08-30 12:31:01 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-08-30 12:31:01 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-08-30 12:31:01 - uvicorn.error - INFO - server - _serve:84 - Started server process [89922]
2025-08-30 12:31:01 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-30 12:31:01 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-30 12:31:01 - uvicorn.error - INFO - server - _log_started_message:216 - Uvicorn running on http://0.0.0.0:8080 (Press CTRL+C to quit)
2025-08-30 12:36:11 - visual_tool - INFO - middleware - dispatch:112 - [TraceID: 1756528571908_6ebd12ee] 请求开始: POST /visual_tools/v1/visual_tool
2025-08-30 12:36:11 - visual_tool - INFO - processor - apply_video_tool:47 - [TraceID: 1756528571908_6ebd12ee] video_download_start
2025-08-30 12:36:11 - visual_tool - INFO - download_util - download_to_tmp_path:48 - [TraceID: 1756528571908_6ebd12ee] download_start: url=http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerJoyrides.mp4, path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp_download_qtr9x_01.mp4, suffix=.mp4
2025-08-30 12:36:13 - visual_tool - ERROR - download_util - download_to_tmp_path:66 - [TraceID: 1756528571908_6ebd12ee] download_attempt_failed: attempt=1, error=('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-08-30 12:36:13 - visual_tool - ERROR - download_util - download_to_tmp_path:66 - [TraceID: 1756528571908_6ebd12ee] download_attempt_failed: attempt=2, error=('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-08-30 12:36:15 - visual_tool - ERROR - download_util - download_to_tmp_path:66 - [TraceID: 1756528571908_6ebd12ee] download_attempt_failed: attempt=3, error=('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-08-30 12:36:15 - visual_tool - ERROR - download_util - download_to_tmp_path:74 - [TraceID: 1756528571908_6ebd12ee] download_failed: attempts=3, error=('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-08-30 12:36:22 - visual_tool - ERROR - processor - apply_video_tool:142 - [TraceID: 1756528571908_6ebd12ee] tmp_cleanup_failed, path=failed to download http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerJoyrides.mp4, error=unlink: path should be string, bytes or os.PathLike, not ValueError
2025-08-30 12:36:22 - visual_tool - INFO - trace_util - log_request_response:151 - [TraceID: 1756528571908_6ebd12ee] 请求数据: {'name': 'video_clip_tool', 'version': '', 'call_id': '', 'arguments': {'start_time': 1, 'end_time': 10}, 'extra_params': {'video_url': 'http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerJoyrides.mp4'}}
2025-08-30 12:36:22 - visual_tool - ERROR - trace_util - log_request_response:155 - [TraceID: 1756528571908_6ebd12ee] 请求处理失败: expected str, bytes or os.PathLike object, not ValueError
2025-08-30 12:36:22 - visual_tool - INFO - middleware - dispatch:132 - [TraceID: 1756528571908_6ebd12ee] 请求完成: 200 - 耗时: 10.6s
2025-08-30 12:37:31 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-30 12:37:31 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-30 12:37:31 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-30 12:37:31 - uvicorn.error - INFO - server - _serve:94 - Finished server process [89922]
2025-08-30 12:37:44 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-08-30 12:37:44 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-08-30 12:37:44 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-08-30 12:37:44 - uvicorn.error - INFO - server - _serve:84 - Started server process [91355]
2025-08-30 12:37:44 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-30 12:37:44 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-30 12:37:44 - uvicorn.error - INFO - server - _log_started_message:216 - Uvicorn running on http://0.0.0.0:8080 (Press CTRL+C to quit)
2025-08-30 12:38:15 - visual_tool - INFO - middleware - dispatch:112 - [TraceID: 1756528695045_fda6e5b0] 请求开始: POST /visual_tools/v1/visual_tool
2025-08-30 12:38:15 - visual_tool - INFO - processor - apply_video_tool:47 - [TraceID: 1756528695045_fda6e5b0] video_download_start
2025-08-30 12:38:15 - visual_tool - INFO - download_util - download_to_tmp_path:48 - [TraceID: 1756528695045_fda6e5b0] download_start: url=http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerJoyrides.mp4, path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp_download_uruoa9xk.mp4, suffix=.mp4
2025-08-30 12:38:16 - visual_tool - INFO - download_util - download_to_tmp_path:60 - [TraceID: 1756528695045_fda6e5b0] download_success: size_kb=2317.21, attempts=1, elapsed_sec=1.818
2025-08-30 12:38:17 - visual_tool - INFO - processor - apply_video_tool:77 - [TraceID: 1756528695045_fda6e5b0] video duration: 15.05
2025-08-30 12:38:17 - visual_tool - INFO - processor - apply_video_tool:140 - [TraceID: 1756528695045_fda6e5b0] tmp_cleanup_ok
2025-08-30 12:38:17 - visual_tool - INFO - trace_util - log_request_response:151 - [TraceID: 1756528695045_fda6e5b0] 请求数据: {'name': 'video_clip_tool', 'version': '', 'call_id': '', 'arguments': {'start_time': 1, 'end_time': 10}, 'extra_params': {'video_url': 'http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerJoyrides.mp4'}}
2025-08-30 12:38:17 - visual_tool - ERROR - trace_util - log_request_response:155 - [TraceID: 1756528695045_fda6e5b0] 请求处理失败: 'VideoFileClip' object has no attribute 'subclip'
2025-08-30 12:38:17 - visual_tool - INFO - middleware - dispatch:132 - [TraceID: 1756528695045_fda6e5b0] 请求完成: 200 - 耗时: 2.2s
2025-08-30 12:40:39 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-30 12:40:39 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-30 12:40:39 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-30 12:40:39 - uvicorn.error - INFO - server - _serve:94 - Finished server process [91355]
2025-08-30 12:40:41 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-08-30 12:40:41 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-08-30 12:40:41 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-08-30 12:40:41 - uvicorn.error - INFO - server - _serve:84 - Started server process [91919]
2025-08-30 12:40:41 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-30 12:40:41 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-30 12:40:41 - uvicorn.error - INFO - server - _log_started_message:216 - Uvicorn running on http://0.0.0.0:8080 (Press CTRL+C to quit)
2025-08-30 12:40:45 - visual_tool - INFO - middleware - dispatch:112 - [TraceID: 1756528845625_b167b77f] 请求开始: POST /visual_tools/v1/visual_tool
2025-08-30 12:40:45 - visual_tool - INFO - processor - apply_video_tool:47 - [TraceID: 1756528845625_b167b77f] video_download_start
2025-08-30 12:40:45 - visual_tool - INFO - download_util - download_to_tmp_path:48 - [TraceID: 1756528845625_b167b77f] download_start: url=http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerJoyrides.mp4, path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp_download_n9kvzjdp.mp4, suffix=.mp4
2025-08-30 12:40:47 - visual_tool - INFO - download_util - download_to_tmp_path:60 - [TraceID: 1756528845625_b167b77f] download_success: size_kb=2317.21, attempts=1, elapsed_sec=2.066
2025-08-30 12:40:48 - visual_tool - INFO - processor - apply_video_tool:77 - [TraceID: 1756528845625_b167b77f] video duration: 15.05
2025-08-30 12:40:50 - visual_tool - INFO - processor - apply_video_tool:86 - [TraceID: 1756528845625_b167b77f] upload_invoke
2025-08-30 12:40:50 - visual_tool - INFO - upload_util - upload_file_to_bos:45 - [TraceID: 1756528845625_b167b77f] upload_start: file_path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp_out_ts473n64.mp4, object_key=visual_tools/output/0580ef4adcd9894dfe660fe3c1e0834f_1756528850593_69330.mp4, size_kb=1878.13
2025-08-30 12:40:50 - visual_tool - INFO - bos_util - get_bos_client:50 - Initializing BOS client...
2025-08-30 12:40:50 - visual_tool - INFO - bos_util - get_bos_client:59 - BOS client initialized successfully
2025-08-30 12:41:01 - visual_tool - ERROR - upload_util - upload_file_to_bos:60 - [TraceID: 1756528845625_b167b77f] upload_failed
2025-08-30 12:41:01 - visual_tool - INFO - processor - apply_video_tool:140 - [TraceID: 1756528845625_b167b77f] tmp_cleanup_ok
2025-08-30 12:41:01 - visual_tool - INFO - trace_util - log_request_response:151 - [TraceID: 1756528845625_b167b77f] 请求数据: {'name': 'video_clip_tool', 'version': '', 'call_id': '', 'arguments': {'start_time': 1, 'end_time': 10}, 'extra_params': {'video_url': 'http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerJoyrides.mp4'}}
2025-08-30 12:41:01 - visual_tool - ERROR - trace_util - log_request_response:155 - [TraceID: 1756528845625_b167b77f] 请求处理失败: Unable to execute HTTP request. Retried 3 times. All trace backs:
>>>>Traceback (most recent call last):
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 210, in send_request
>>>>    http_response = _send_http_request(
>>>>                    ^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 101, in _send_http_request
>>>>    conn.send(buf)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/http/client.py", line 1019, in send
>>>>    self.sock.sendall(data)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1273, in sendall
>>>>    v = self.send(byte_view[count:])
>>>>        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1242, in send
>>>>    return self._sslobj.write(data)
>>>>           ^^^^^^^^^^^^^^^^^^^^^^^^
>>>>TimeoutError: The write operation timed out
>>>>Traceback (most recent call last):
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 210, in send_request
>>>>    http_response = _send_http_request(
>>>>                    ^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 101, in _send_http_request
>>>>    conn.send(buf)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/http/client.py", line 1019, in send
>>>>    self.sock.sendall(data)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1273, in sendall
>>>>    v = self.send(byte_view[count:])
>>>>        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1242, in send
>>>>    return self._sslobj.write(data)
>>>>           ^^^^^^^^^^^^^^^^^^^^^^^^
>>>>TimeoutError: The write operation timed out
>>>>Traceback (most recent call last):
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 210, in send_request
>>>>    http_response = _send_http_request(
>>>>                    ^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 101, in _send_http_request
>>>>    conn.send(buf)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/http/client.py", line 1019, in send
>>>>    self.sock.sendall(data)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1273, in sendall
>>>>    v = self.send(byte_view[count:])
>>>>        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1242, in send
>>>>    return self._sslobj.write(data)
>>>>           ^^^^^^^^^^^^^^^^^^^^^^^^
>>>>TimeoutError: The write operation timed out
>>>>Traceback (most recent call last):
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 210, in send_request
>>>>    http_response = _send_http_request(
>>>>                    ^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 101, in _send_http_request
>>>>    conn.send(buf)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/http/client.py", line 1019, in send
>>>>    self.sock.sendall(data)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1273, in sendall
>>>>    v = self.send(byte_view[count:])
>>>>        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1242, in send
>>>>    return self._sslobj.write(data)
>>>>           ^^^^^^^^^^^^^^^^^^^^^^^^
>>>>TimeoutError: The write operation timed out
2025-08-30 12:41:01 - visual_tool - INFO - middleware - dispatch:132 - [TraceID: 1756528845625_b167b77f] 请求完成: 200 - 耗时: 15.6s
2025-08-30 12:41:25 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-30 12:41:25 - uvicorn.error - INFO - server - _serve:94 - Finished server process [91919]
2025-08-30 12:41:25 - uvicorn.error - ERROR - on - send:134 - Traceback (most recent call last):
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/asyncio/base_events.py", line 641, in run_until_complete
    self.run_forever()
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/asyncio/base_events.py", line 608, in run_forever
    self._run_once()
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/asyncio/base_events.py", line 1936, in _run_once
    handle._run()
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/asyncio/events.py", line 84, in _run
    self._context.run(self._callback, *self._args)
  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 70, in serve
    with self.capture_signals():
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/contextlib.py", line 144, in __exit__
    next(self.gen)
  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 331, in capture_signals
    signal.raise_signal(captured_signal)
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/asyncio/runners.py", line 157, in _on_sigint
    raise KeyboardInterrupt()
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/starlette/routing.py", line 701, in lifespan
    await receive()
  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/uvicorn/lifespan/on.py", line 137, in receive
    return await self.receive_queue.get()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/asyncio/queues.py", line 158, in get
    await getter
asyncio.exceptions.CancelledError

2025-08-30 12:43:59 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-08-30 12:43:59 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-08-30 12:43:59 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-08-30 12:43:59 - uvicorn.error - INFO - server - _serve:84 - Started server process [93227]
2025-08-30 12:43:59 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-30 12:43:59 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-30 12:43:59 - uvicorn.error - INFO - server - _log_started_message:216 - Uvicorn running on http://0.0.0.0:8080 (Press CTRL+C to quit)
2025-08-30 12:44:20 - visual_tool - INFO - middleware - dispatch:112 - [TraceID: 1756529060890_d0832573] 请求开始: POST /visual_tools/v1/visual_tool
2025-08-30 12:44:20 - visual_tool - INFO - processor - apply_video_tool:47 - [TraceID: 1756529060890_d0832573] video_download_start
2025-08-30 12:44:20 - visual_tool - INFO - download_util - download_to_tmp_path:48 - [TraceID: 1756529060890_d0832573] download_start: url=https://mcp-env.bj.bcebos.com/v1/tmp/ForBiggerJoyrides.mp4?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T04%3A43%3A42Z%2F-1%2Fhost%2F0f079e07d12fc923c0ad3038821969c4dd3f6ee8e8c580bb956b7858db2b629a, path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp_download_lkwcyc1p.mp4, suffix=.mp4
2025-08-30 12:44:21 - visual_tool - INFO - download_util - download_to_tmp_path:60 - [TraceID: 1756529060890_d0832573] download_success: size_kb=2317.21, attempts=1, elapsed_sec=0.62
2025-08-30 12:44:21 - visual_tool - INFO - processor - apply_video_tool:77 - [TraceID: 1756529060890_d0832573] video duration: 15.05
2025-08-30 12:44:24 - visual_tool - INFO - processor - apply_video_tool:86 - [TraceID: 1756529060890_d0832573] upload_invoke
2025-08-30 12:44:24 - visual_tool - INFO - upload_util - upload_file_to_bos:45 - [TraceID: 1756529060890_d0832573] upload_start: file_path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp_out_3cv_gqcv.mp4, object_key=visual_tools/output/0580ef4adcd9894dfe660fe3c1e0834f_1756529064307_12821.mp4, size_kb=1878.13
2025-08-30 12:44:24 - visual_tool - INFO - bos_util - get_bos_client:50 - Initializing BOS client...
2025-08-30 12:44:24 - visual_tool - INFO - bos_util - get_bos_client:59 - BOS client initialized successfully
2025-08-30 12:44:34 - visual_tool - ERROR - upload_util - upload_file_to_bos:60 - [TraceID: 1756529060890_d0832573] upload_failed
2025-08-30 12:44:34 - visual_tool - INFO - processor - apply_video_tool:140 - [TraceID: 1756529060890_d0832573] tmp_cleanup_ok
2025-08-30 12:44:34 - visual_tool - INFO - trace_util - log_request_response:151 - [TraceID: 1756529060890_d0832573] 请求数据: {'name': 'video_clip_tool', 'version': '', 'call_id': '', 'arguments': {'start_time': 1, 'end_time': 10}, 'extra_params': {'video_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/ForBiggerJoyrides.mp4?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T04%3A43%3A42Z%2F-1%2Fhost%2F0f079e07d12fc923c0ad3038821969c4dd3f6ee8e8c580bb956b7858db2b629a'}}
2025-08-30 12:44:34 - visual_tool - ERROR - trace_util - log_request_response:155 - [TraceID: 1756529060890_d0832573] 请求处理失败: Unable to execute HTTP request. Retried 3 times. All trace backs:
>>>>Traceback (most recent call last):
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 210, in send_request
>>>>    http_response = _send_http_request(
>>>>                    ^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 101, in _send_http_request
>>>>    conn.send(buf)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/http/client.py", line 1019, in send
>>>>    self.sock.sendall(data)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1273, in sendall
>>>>    v = self.send(byte_view[count:])
>>>>        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1242, in send
>>>>    return self._sslobj.write(data)
>>>>           ^^^^^^^^^^^^^^^^^^^^^^^^
>>>>TimeoutError: The write operation timed out
>>>>Traceback (most recent call last):
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 210, in send_request
>>>>    http_response = _send_http_request(
>>>>                    ^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 101, in _send_http_request
>>>>    conn.send(buf)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/http/client.py", line 1019, in send
>>>>    self.sock.sendall(data)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1273, in sendall
>>>>    v = self.send(byte_view[count:])
>>>>        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1242, in send
>>>>    return self._sslobj.write(data)
>>>>           ^^^^^^^^^^^^^^^^^^^^^^^^
>>>>TimeoutError: The write operation timed out
>>>>Traceback (most recent call last):
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 210, in send_request
>>>>    http_response = _send_http_request(
>>>>                    ^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 101, in _send_http_request
>>>>    conn.send(buf)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/http/client.py", line 1019, in send
>>>>    self.sock.sendall(data)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1273, in sendall
>>>>    v = self.send(byte_view[count:])
>>>>        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1242, in send
>>>>    return self._sslobj.write(data)
>>>>           ^^^^^^^^^^^^^^^^^^^^^^^^
>>>>TimeoutError: The write operation timed out
>>>>Traceback (most recent call last):
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 210, in send_request
>>>>    http_response = _send_http_request(
>>>>                    ^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 101, in _send_http_request
>>>>    conn.send(buf)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/http/client.py", line 1019, in send
>>>>    self.sock.sendall(data)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1273, in sendall
>>>>    v = self.send(byte_view[count:])
>>>>        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1242, in send
>>>>    return self._sslobj.write(data)
>>>>           ^^^^^^^^^^^^^^^^^^^^^^^^
>>>>TimeoutError: The write operation timed out
2025-08-30 12:44:34 - visual_tool - INFO - middleware - dispatch:132 - [TraceID: 1756529060890_d0832573] 请求完成: 200 - 耗时: 13.9s
2025-08-30 12:48:02 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-30 12:48:02 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-30 12:48:02 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-30 12:48:02 - uvicorn.error - INFO - server - _serve:94 - Finished server process [93227]
2025-08-30 12:50:21 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-08-30 12:50:21 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-08-30 12:50:21 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-08-30 12:50:21 - uvicorn.error - INFO - server - _serve:84 - Started server process [94526]
2025-08-30 12:50:21 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-30 12:50:21 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-30 12:50:21 - uvicorn.error - INFO - server - _log_started_message:216 - Uvicorn running on http://0.0.0.0:8080 (Press CTRL+C to quit)
2025-08-30 12:50:28 - visual_tool - INFO - middleware - dispatch:112 - [TraceID: 1756529428293_84c2565f] 请求开始: POST /visual_tools/v1/visual_tool
2025-08-30 12:50:28 - visual_tool - INFO - processor - apply_image_tool:87 - [TraceID: 1756529428293_84c2565f] image_download_start
2025-08-30 12:50:28 - visual_tool - INFO - download_util - download_to_tmp_path:48 - [TraceID: 1756529428293_84c2565f] download_start: url=https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073, path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp_download_yutfn4he.jpeg, suffix=.jpeg
2025-08-30 12:50:28 - visual_tool - INFO - download_util - download_to_tmp_path:60 - [TraceID: 1756529428293_84c2565f] download_success: size_kb=31.93, attempts=1, elapsed_sec=0.163
2025-08-30 12:50:28 - visual_tool - INFO - processor - apply_image_tool:91 - [TraceID: 1756529428293_84c2565f] image_download_success
2025-08-30 12:50:28 - visual_tool - INFO - processor - apply_image_tool:112 - [TraceID: 1756529428293_84c2565f] image_processing_start: tool=image_crop_by_abs_coord_tool, width= 554, height= 554
2025-08-30 12:50:28 - visual_tool - INFO - processor - apply_image_tool:253 - [TraceID: 1756529428293_84c2565f] upload_invoke
2025-08-30 12:50:28 - visual_tool - INFO - upload_util - upload_file_to_bos:45 - [TraceID: 1756529428293_84c2565f] upload_start: file_path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmpj653tvyl.jpeg, object_key=visual_tools/output/669a49214786b1141fcdf61f13b9dc62_1756529428488_75583.jpeg, size_kb=7.01
2025-08-30 12:50:28 - visual_tool - INFO - bos_util - get_bos_client:50 - Initializing BOS client...
2025-08-30 12:50:28 - visual_tool - INFO - bos_util - get_bos_client:59 - BOS client initialized successfully
2025-08-30 12:50:28 - visual_tool - INFO - upload_util - upload_file_to_bos:57 - [TraceID: 1756529428293_84c2565f] upload_success
2025-08-30 12:50:28 - visual_tool - INFO - processor - apply_image_tool:257 - [TraceID: 1756529428293_84c2565f] image_processing_done
2025-08-30 12:50:28 - visual_tool - INFO - trace_util - log_request_response:151 - [TraceID: 1756529428293_84c2565f] 请求数据: {'name': 'image_zoom_in_tool', 'version': '', 'call_id': '', 'arguments': {'norm': False, 'bbox_2d': [0, 0, 256, 256]}, 'extra_params': {'image_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073', 'image_data': '', 'return_data': False}}
2025-08-30 12:50:28 - visual_tool - INFO - trace_util - log_request_response:158 - [TraceID: 1756529428293_84c2565f] 响应数据: {'code': 0, 'message': 'Finish', 'data': '{"type": "image_url", "image_url": "https://mcp-env.bj.bcebos.com/visual_tools/output/669a49214786b1141fcdf61f13b9dc62_1756529428488_75583.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T04%3A50%3A28Z%2F-1%2F%2Faa297e31bf5ddb14a45a1dea4413710448a9d7ae380368d320a1143e8dca1318"}'}
2025-08-30 12:50:28 - visual_tool - INFO - middleware - dispatch:132 - [TraceID: 1756529428293_84c2565f] 请求完成: 200 - 耗时: 529ms
2025-08-30 12:50:33 - visual_tool - INFO - middleware - dispatch:112 - [TraceID: 1756529433269_44796be7] 请求开始: POST /visual_tools/v1/visual_tool
2025-08-30 12:50:33 - visual_tool - INFO - processor - apply_video_tool:47 - [TraceID: 1756529433269_44796be7] video_download_start
2025-08-30 12:50:33 - visual_tool - INFO - download_util - download_to_tmp_path:48 - [TraceID: 1756529433269_44796be7] download_start: url=https://mcp-env.bj.bcebos.com/v1/tmp/ForBiggerJoyrides.mp4?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T04%3A43%3A42Z%2F-1%2Fhost%2F0f079e07d12fc923c0ad3038821969c4dd3f6ee8e8c580bb956b7858db2b629a, path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp_download_4od1_29k.mp4, suffix=.mp4
2025-08-30 12:50:33 - visual_tool - INFO - download_util - download_to_tmp_path:60 - [TraceID: 1756529433269_44796be7] download_success: size_kb=2317.21, attempts=1, elapsed_sec=0.699
2025-08-30 12:50:34 - visual_tool - INFO - processor - apply_video_tool:77 - [TraceID: 1756529433269_44796be7] video duration: 15.05
2025-08-30 12:50:36 - visual_tool - INFO - processor - apply_video_tool:86 - [TraceID: 1756529433269_44796be7] upload_invoke
2025-08-30 12:50:36 - visual_tool - INFO - upload_util - upload_file_to_bos:45 - [TraceID: 1756529433269_44796be7] upload_start: file_path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp_out_kookxvzg.mp4, object_key=visual_tools/output/0580ef4adcd9894dfe660fe3c1e0834f_1756529436806_10375.mp4, size_kb=1878.13
2025-08-30 12:50:47 - visual_tool - ERROR - upload_util - upload_file_to_bos:60 - [TraceID: 1756529433269_44796be7] upload_failed
2025-08-30 12:50:47 - visual_tool - INFO - processor - apply_video_tool:140 - [TraceID: 1756529433269_44796be7] tmp_cleanup_ok
2025-08-30 12:50:47 - visual_tool - INFO - trace_util - log_request_response:151 - [TraceID: 1756529433269_44796be7] 请求数据: {'name': 'video_clip_tool', 'version': '', 'call_id': '', 'arguments': {'start_time': 1, 'end_time': 10}, 'extra_params': {'video_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/ForBiggerJoyrides.mp4?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T04%3A43%3A42Z%2F-1%2Fhost%2F0f079e07d12fc923c0ad3038821969c4dd3f6ee8e8c580bb956b7858db2b629a'}}
2025-08-30 12:50:47 - visual_tool - ERROR - trace_util - log_request_response:155 - [TraceID: 1756529433269_44796be7] 请求处理失败: Unable to execute HTTP request. Retried 3 times. All trace backs:
>>>>Traceback (most recent call last):
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 210, in send_request
>>>>    http_response = _send_http_request(
>>>>                    ^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 101, in _send_http_request
>>>>    conn.send(buf)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/http/client.py", line 1019, in send
>>>>    self.sock.sendall(data)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1273, in sendall
>>>>    v = self.send(byte_view[count:])
>>>>        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1242, in send
>>>>    return self._sslobj.write(data)
>>>>           ^^^^^^^^^^^^^^^^^^^^^^^^
>>>>TimeoutError: The write operation timed out
>>>>Traceback (most recent call last):
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 210, in send_request
>>>>    http_response = _send_http_request(
>>>>                    ^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 101, in _send_http_request
>>>>    conn.send(buf)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/http/client.py", line 1019, in send
>>>>    self.sock.sendall(data)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1273, in sendall
>>>>    v = self.send(byte_view[count:])
>>>>        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1242, in send
>>>>    return self._sslobj.write(data)
>>>>           ^^^^^^^^^^^^^^^^^^^^^^^^
>>>>TimeoutError: The write operation timed out
>>>>Traceback (most recent call last):
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 210, in send_request
>>>>    http_response = _send_http_request(
>>>>                    ^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 101, in _send_http_request
>>>>    conn.send(buf)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/http/client.py", line 1019, in send
>>>>    self.sock.sendall(data)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1273, in sendall
>>>>    v = self.send(byte_view[count:])
>>>>        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1242, in send
>>>>    return self._sslobj.write(data)
>>>>           ^^^^^^^^^^^^^^^^^^^^^^^^
>>>>TimeoutError: The write operation timed out
>>>>Traceback (most recent call last):
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 210, in send_request
>>>>    http_response = _send_http_request(
>>>>                    ^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 101, in _send_http_request
>>>>    conn.send(buf)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/http/client.py", line 1019, in send
>>>>    self.sock.sendall(data)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1273, in sendall
>>>>    v = self.send(byte_view[count:])
>>>>        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1242, in send
>>>>    return self._sslobj.write(data)
>>>>           ^^^^^^^^^^^^^^^^^^^^^^^^
>>>>TimeoutError: The write operation timed out
2025-08-30 12:50:47 - visual_tool - INFO - middleware - dispatch:132 - [TraceID: 1756529433269_44796be7] 请求完成: 200 - 耗时: 14.1s
2025-08-30 12:51:50 - visual_tool - INFO - middleware - dispatch:112 - [TraceID: 1756529510831_91e769c5] 请求开始: POST /visual_tools/v1/visual_tool
2025-08-30 12:51:50 - visual_tool - INFO - processor - apply_image_tool:87 - [TraceID: 1756529510831_91e769c5] image_download_start
2025-08-30 12:51:50 - visual_tool - INFO - download_util - download_to_tmp_path:48 - [TraceID: 1756529510831_91e769c5] download_start: url=https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073, path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp_download_wtm5esk9.jpeg, suffix=.jpeg
2025-08-30 12:51:51 - visual_tool - INFO - download_util - download_to_tmp_path:60 - [TraceID: 1756529510831_91e769c5] download_success: size_kb=31.93, attempts=1, elapsed_sec=0.236
2025-08-30 12:51:51 - visual_tool - INFO - processor - apply_image_tool:91 - [TraceID: 1756529510831_91e769c5] image_download_success
2025-08-30 12:51:51 - visual_tool - INFO - processor - apply_image_tool:112 - [TraceID: 1756529510831_91e769c5] image_processing_start: tool=image_crop_by_abs_coord_tool, width= 554, height= 554
2025-08-30 12:51:51 - visual_tool - INFO - processor - apply_image_tool:253 - [TraceID: 1756529510831_91e769c5] upload_invoke
2025-08-30 12:51:51 - visual_tool - INFO - upload_util - upload_file_to_bos:45 - [TraceID: 1756529510831_91e769c5] upload_start: file_path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmptmuqjfza.jpeg, object_key=visual_tools/output/669a49214786b1141fcdf61f13b9dc62_1756529511079_56897.jpeg, size_kb=7.01
2025-08-30 12:51:51 - visual_tool - INFO - upload_util - upload_file_to_bos:57 - [TraceID: 1756529510831_91e769c5] upload_success
2025-08-30 12:51:51 - visual_tool - INFO - processor - apply_image_tool:257 - [TraceID: 1756529510831_91e769c5] image_processing_done
2025-08-30 12:51:51 - visual_tool - INFO - trace_util - log_request_response:151 - [TraceID: 1756529510831_91e769c5] 请求数据: {'name': 'image_zoom_in_tool', 'version': '', 'call_id': '', 'arguments': {'norm': False, 'bbox_2d': [0, 0, 256, 256]}, 'extra_params': {'image_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073', 'image_data': '', 'return_data': False}}
2025-08-30 12:51:51 - visual_tool - INFO - trace_util - log_request_response:158 - [TraceID: 1756529510831_91e769c5] 响应数据: {'code': 0, 'message': 'Finish', 'data': '{"type": "image_url", "image_url": "https://mcp-env.bj.bcebos.com/visual_tools/output/669a49214786b1141fcdf61f13b9dc62_1756529511079_56897.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T04%3A51%3A51Z%2F-1%2F%2Fe929e40e8817f8e02e527081a3b8b44c71c7617343d21d920d65495975a9f1d4"}'}
2025-08-30 12:51:51 - visual_tool - INFO - middleware - dispatch:132 - [TraceID: 1756529510831_91e769c5] 请求完成: 200 - 耗时: 795ms
2025-08-30 12:51:53 - visual_tool - INFO - middleware - dispatch:112 - [TraceID: 1756529513259_52e2691c] 请求开始: POST /visual_tools/v1/visual_tool
2025-08-30 12:51:53 - visual_tool - INFO - processor - apply_image_tool:87 - [TraceID: 1756529513259_52e2691c] image_download_start
2025-08-30 12:51:53 - visual_tool - INFO - download_util - download_to_tmp_path:48 - [TraceID: 1756529513259_52e2691c] download_start: url=https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073, path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp_download_j950guc5.jpeg, suffix=.jpeg
2025-08-30 12:51:53 - visual_tool - INFO - download_util - download_to_tmp_path:60 - [TraceID: 1756529513259_52e2691c] download_success: size_kb=31.93, attempts=1, elapsed_sec=0.166
2025-08-30 12:51:53 - visual_tool - INFO - processor - apply_image_tool:91 - [TraceID: 1756529513259_52e2691c] image_download_success
2025-08-30 12:51:53 - visual_tool - INFO - processor - apply_image_tool:112 - [TraceID: 1756529513259_52e2691c] image_processing_start: tool=image_crop_by_abs_coord_tool, width= 554, height= 554
2025-08-30 12:51:53 - visual_tool - INFO - processor - apply_image_tool:253 - [TraceID: 1756529513259_52e2691c] upload_invoke
2025-08-30 12:51:53 - visual_tool - INFO - upload_util - upload_file_to_bos:45 - [TraceID: 1756529513259_52e2691c] upload_start: file_path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmphlm4i72a.jpeg, object_key=visual_tools/output/669a49214786b1141fcdf61f13b9dc62_1756529513434_34952.jpeg, size_kb=7.01
2025-08-30 12:51:53 - visual_tool - INFO - upload_util - upload_file_to_bos:57 - [TraceID: 1756529513259_52e2691c] upload_success
2025-08-30 12:51:53 - visual_tool - INFO - processor - apply_image_tool:257 - [TraceID: 1756529513259_52e2691c] image_processing_done
2025-08-30 12:51:53 - visual_tool - INFO - trace_util - log_request_response:151 - [TraceID: 1756529513259_52e2691c] 请求数据: {'name': 'image_zoom_in_tool', 'version': '', 'call_id': '', 'arguments': {'norm': False, 'bbox_2d': [0, 0, 256, 256]}, 'extra_params': {'image_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073', 'image_data': '', 'return_data': False}}
2025-08-30 12:51:53 - visual_tool - INFO - trace_util - log_request_response:158 - [TraceID: 1756529513259_52e2691c] 响应数据: {'code': 0, 'message': 'Finish', 'data': '{"type": "image_url", "image_url": "https://mcp-env.bj.bcebos.com/visual_tools/output/669a49214786b1141fcdf61f13b9dc62_1756529513434_34952.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T04%3A51%3A53Z%2F-1%2F%2F2ab446b5f815519b1d9331eec86b099107de85f314959c6903aa3085b8c45efb"}'}
2025-08-30 12:51:53 - visual_tool - INFO - middleware - dispatch:132 - [TraceID: 1756529513259_52e2691c] 请求完成: 200 - 耗时: 310ms
2025-08-30 12:51:54 - visual_tool - INFO - middleware - dispatch:112 - [TraceID: 1756529514430_56f0aad6] 请求开始: POST /visual_tools/v1/visual_tool
2025-08-30 12:51:54 - visual_tool - INFO - processor - apply_image_tool:87 - [TraceID: 1756529514430_56f0aad6] image_download_start
2025-08-30 12:51:54 - visual_tool - INFO - download_util - download_to_tmp_path:48 - [TraceID: 1756529514430_56f0aad6] download_start: url=https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073, path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp_download_42xriffe.jpeg, suffix=.jpeg
2025-08-30 12:51:54 - visual_tool - INFO - download_util - download_to_tmp_path:60 - [TraceID: 1756529514430_56f0aad6] download_success: size_kb=31.93, attempts=1, elapsed_sec=0.148
2025-08-30 12:51:54 - visual_tool - INFO - processor - apply_image_tool:91 - [TraceID: 1756529514430_56f0aad6] image_download_success
2025-08-30 12:51:54 - visual_tool - INFO - processor - apply_image_tool:112 - [TraceID: 1756529514430_56f0aad6] image_processing_start: tool=image_crop_by_abs_coord_tool, width= 554, height= 554
2025-08-30 12:51:54 - visual_tool - INFO - processor - apply_image_tool:253 - [TraceID: 1756529514430_56f0aad6] upload_invoke
2025-08-30 12:51:54 - visual_tool - INFO - upload_util - upload_file_to_bos:45 - [TraceID: 1756529514430_56f0aad6] upload_start: file_path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp40xag0ie.jpeg, object_key=visual_tools/output/669a49214786b1141fcdf61f13b9dc62_1756529514584_15464.jpeg, size_kb=7.01
2025-08-30 12:51:54 - visual_tool - INFO - upload_util - upload_file_to_bos:57 - [TraceID: 1756529514430_56f0aad6] upload_success
2025-08-30 12:51:54 - visual_tool - INFO - processor - apply_image_tool:257 - [TraceID: 1756529514430_56f0aad6] image_processing_done
2025-08-30 12:51:54 - visual_tool - INFO - trace_util - log_request_response:151 - [TraceID: 1756529514430_56f0aad6] 请求数据: {'name': 'image_zoom_in_tool', 'version': '', 'call_id': '', 'arguments': {'norm': False, 'bbox_2d': [0, 0, 256, 256]}, 'extra_params': {'image_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073', 'image_data': '', 'return_data': False}}
2025-08-30 12:51:54 - visual_tool - INFO - trace_util - log_request_response:158 - [TraceID: 1756529514430_56f0aad6] 响应数据: {'code': 0, 'message': 'Finish', 'data': '{"type": "image_url", "image_url": "https://mcp-env.bj.bcebos.com/visual_tools/output/669a49214786b1141fcdf61f13b9dc62_1756529514584_15464.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T04%3A51%3A54Z%2F-1%2F%2Fce1c452339afc5f0d409efed060f6d14c8c0311707cc3763ff1c668aa0505b06"}'}
2025-08-30 12:51:54 - visual_tool - INFO - middleware - dispatch:132 - [TraceID: 1756529514430_56f0aad6] 请求完成: 200 - 耗时: 293ms
2025-08-30 12:51:55 - visual_tool - INFO - middleware - dispatch:112 - [TraceID: 1756529515479_1a11e716] 请求开始: POST /visual_tools/v1/visual_tool
2025-08-30 12:51:55 - visual_tool - INFO - processor - apply_image_tool:87 - [TraceID: 1756529515479_1a11e716] image_download_start
2025-08-30 12:51:55 - visual_tool - INFO - download_util - download_to_tmp_path:48 - [TraceID: 1756529515479_1a11e716] download_start: url=https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073, path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp_download_wv237i9a.jpeg, suffix=.jpeg
2025-08-30 12:51:55 - visual_tool - INFO - download_util - download_to_tmp_path:60 - [TraceID: 1756529515479_1a11e716] download_success: size_kb=31.93, attempts=1, elapsed_sec=0.132
2025-08-30 12:51:55 - visual_tool - INFO - processor - apply_image_tool:91 - [TraceID: 1756529515479_1a11e716] image_download_success
2025-08-30 12:51:55 - visual_tool - INFO - processor - apply_image_tool:112 - [TraceID: 1756529515479_1a11e716] image_processing_start: tool=image_crop_by_abs_coord_tool, width= 554, height= 554
2025-08-30 12:51:55 - visual_tool - INFO - processor - apply_image_tool:253 - [TraceID: 1756529515479_1a11e716] upload_invoke
2025-08-30 12:51:55 - visual_tool - INFO - upload_util - upload_file_to_bos:45 - [TraceID: 1756529515479_1a11e716] upload_start: file_path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmpxoevp6ef.jpeg, object_key=visual_tools/output/669a49214786b1141fcdf61f13b9dc62_1756529515617_59349.jpeg, size_kb=7.01
2025-08-30 12:51:55 - visual_tool - INFO - upload_util - upload_file_to_bos:57 - [TraceID: 1756529515479_1a11e716] upload_success
2025-08-30 12:51:55 - visual_tool - INFO - processor - apply_image_tool:257 - [TraceID: 1756529515479_1a11e716] image_processing_done
2025-08-30 12:51:55 - visual_tool - INFO - trace_util - log_request_response:151 - [TraceID: 1756529515479_1a11e716] 请求数据: {'name': 'image_zoom_in_tool', 'version': '', 'call_id': '', 'arguments': {'norm': False, 'bbox_2d': [0, 0, 256, 256]}, 'extra_params': {'image_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073', 'image_data': '', 'return_data': False}}
2025-08-30 12:51:55 - visual_tool - INFO - trace_util - log_request_response:158 - [TraceID: 1756529515479_1a11e716] 响应数据: {'code': 0, 'message': 'Finish', 'data': '{"type": "image_url", "image_url": "https://mcp-env.bj.bcebos.com/visual_tools/output/669a49214786b1141fcdf61f13b9dc62_1756529515617_59349.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T04%3A51%3A55Z%2F-1%2F%2F5ed8d14300da975011e37d52e0a32c18d5a7430c2d73126e06a28c06979c841c"}'}
2025-08-30 12:51:55 - visual_tool - INFO - middleware - dispatch:132 - [TraceID: 1756529515479_1a11e716] 请求完成: 200 - 耗时: 292ms
2025-08-30 12:53:19 - visual_tool - INFO - middleware - dispatch:112 - [TraceID: 1756529599025_09cfba82] 请求开始: POST /visual_tools/v1/visual_tool
2025-08-30 12:53:19 - visual_tool - INFO - processor - apply_image_tool:87 - [TraceID: 1756529599025_09cfba82] image_download_start
2025-08-30 12:53:19 - visual_tool - INFO - download_util - download_to_tmp_path:48 - [TraceID: 1756529599025_09cfba82] download_start: url=https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073, path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp_download_ztlrxsci.jpeg, suffix=.jpeg
2025-08-30 12:53:19 - visual_tool - INFO - download_util - download_to_tmp_path:60 - [TraceID: 1756529599025_09cfba82] download_success: size_kb=31.93, attempts=1, elapsed_sec=0.154
2025-08-30 12:53:19 - visual_tool - INFO - processor - apply_image_tool:91 - [TraceID: 1756529599025_09cfba82] image_download_success
2025-08-30 12:53:19 - visual_tool - INFO - processor - apply_image_tool:112 - [TraceID: 1756529599025_09cfba82] image_processing_start: tool=image_crop_by_abs_coord_tool, width= 554, height= 554
2025-08-30 12:53:19 - visual_tool - INFO - processor - apply_image_tool:253 - [TraceID: 1756529599025_09cfba82] upload_invoke
2025-08-30 12:53:19 - visual_tool - INFO - upload_util - upload_file_to_bos:45 - [TraceID: 1756529599025_09cfba82] upload_start: file_path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp6gb5mdca.jpeg, object_key=visual_tools/output/669a49214786b1141fcdf61f13b9dc62_1756529599195_81721.jpeg, size_kb=7.01
2025-08-30 12:53:19 - visual_tool - INFO - upload_util - upload_file_to_bos:57 - [TraceID: 1756529599025_09cfba82] upload_success
2025-08-30 12:53:19 - visual_tool - INFO - processor - apply_image_tool:257 - [TraceID: 1756529599025_09cfba82] image_processing_done
2025-08-30 12:53:19 - visual_tool - INFO - trace_util - log_request_response:151 - [TraceID: 1756529599025_09cfba82] 请求数据: {'name': 'image_zoom_in_tool', 'version': '', 'call_id': '', 'arguments': {'norm': False, 'bbox_2d': [0, 0, 256, 256]}, 'extra_params': {'image_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073', 'image_data': '', 'return_data': False}}
2025-08-30 12:53:19 - visual_tool - INFO - trace_util - log_request_response:158 - [TraceID: 1756529599025_09cfba82] 响应数据: {'code': 0, 'message': 'Finish', 'data': '{"type": "image_url", "image_url": "https://mcp-env.bj.bcebos.com/visual_tools/output/669a49214786b1141fcdf61f13b9dc62_1756529599195_81721.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T04%3A53%3A19Z%2F-1%2F%2Fa1ffeeedff696c9567b24fab452d59788a4480c849fc7f0670aa9b89ca8124d6"}'}
2025-08-30 12:53:19 - visual_tool - INFO - middleware - dispatch:132 - [TraceID: 1756529599025_09cfba82] 请求完成: 200 - 耗时: 531ms
2025-08-30 12:53:31 - visual_tool - INFO - middleware - dispatch:112 - [TraceID: 1756529611941_25ff9437] 请求开始: POST /visual_tools/v1/visual_tool
2025-08-30 12:53:31 - visual_tool - INFO - processor - apply_video_tool:47 - [TraceID: 1756529611941_25ff9437] video_download_start
2025-08-30 12:53:31 - visual_tool - INFO - download_util - download_to_tmp_path:48 - [TraceID: 1756529611941_25ff9437] download_start: url=https://mcp-env.bj.bcebos.com/v1/tmp/ForBiggerJoyrides.mp4?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T04%3A43%3A42Z%2F-1%2Fhost%2F0f079e07d12fc923c0ad3038821969c4dd3f6ee8e8c580bb956b7858db2b629a, path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp_download_axpaec65.mp4, suffix=.mp4
2025-08-30 12:53:32 - visual_tool - INFO - download_util - download_to_tmp_path:60 - [TraceID: 1756529611941_25ff9437] download_success: size_kb=2317.21, attempts=1, elapsed_sec=0.617
2025-08-30 12:53:32 - visual_tool - INFO - processor - apply_video_tool:77 - [TraceID: 1756529611941_25ff9437] video duration: 15.05
2025-08-30 12:53:35 - visual_tool - INFO - processor - apply_video_tool:86 - [TraceID: 1756529611941_25ff9437] upload_invoke
2025-08-30 12:53:35 - visual_tool - INFO - upload_util - upload_file_to_bos:45 - [TraceID: 1756529611941_25ff9437] upload_start: file_path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp_out_m57mcnrw.mp4, object_key=visual_tools/output/0580ef4adcd9894dfe660fe3c1e0834f_1756529615268_41829.mp4, size_kb=1878.13
2025-08-30 12:53:45 - visual_tool - ERROR - upload_util - upload_file_to_bos:60 - [TraceID: 1756529611941_25ff9437] upload_failed
2025-08-30 12:53:45 - visual_tool - INFO - processor - apply_video_tool:140 - [TraceID: 1756529611941_25ff9437] tmp_cleanup_ok
2025-08-30 12:53:45 - visual_tool - INFO - trace_util - log_request_response:151 - [TraceID: 1756529611941_25ff9437] 请求数据: {'name': 'video_clip_tool', 'version': '', 'call_id': '', 'arguments': {'start_time': 1, 'end_time': 10}, 'extra_params': {'video_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/ForBiggerJoyrides.mp4?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T04%3A43%3A42Z%2F-1%2Fhost%2F0f079e07d12fc923c0ad3038821969c4dd3f6ee8e8c580bb956b7858db2b629a'}}
2025-08-30 12:53:45 - visual_tool - ERROR - trace_util - log_request_response:155 - [TraceID: 1756529611941_25ff9437] 请求处理失败: Unable to execute HTTP request. Retried 3 times. All trace backs:
>>>>Traceback (most recent call last):
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 210, in send_request
>>>>    http_response = _send_http_request(
>>>>                    ^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 101, in _send_http_request
>>>>    conn.send(buf)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/http/client.py", line 1019, in send
>>>>    self.sock.sendall(data)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1273, in sendall
>>>>    v = self.send(byte_view[count:])
>>>>        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1242, in send
>>>>    return self._sslobj.write(data)
>>>>           ^^^^^^^^^^^^^^^^^^^^^^^^
>>>>TimeoutError: The write operation timed out
>>>>Traceback (most recent call last):
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 210, in send_request
>>>>    http_response = _send_http_request(
>>>>                    ^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 101, in _send_http_request
>>>>    conn.send(buf)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/http/client.py", line 1019, in send
>>>>    self.sock.sendall(data)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1273, in sendall
>>>>    v = self.send(byte_view[count:])
>>>>        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1242, in send
>>>>    return self._sslobj.write(data)
>>>>           ^^^^^^^^^^^^^^^^^^^^^^^^
>>>>TimeoutError: The write operation timed out
>>>>Traceback (most recent call last):
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 210, in send_request
>>>>    http_response = _send_http_request(
>>>>                    ^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 101, in _send_http_request
>>>>    conn.send(buf)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/http/client.py", line 1019, in send
>>>>    self.sock.sendall(data)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1273, in sendall
>>>>    v = self.send(byte_view[count:])
>>>>        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1242, in send
>>>>    return self._sslobj.write(data)
>>>>           ^^^^^^^^^^^^^^^^^^^^^^^^
>>>>TimeoutError: The write operation timed out
>>>>Traceback (most recent call last):
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 210, in send_request
>>>>    http_response = _send_http_request(
>>>>                    ^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 101, in _send_http_request
>>>>    conn.send(buf)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/http/client.py", line 1019, in send
>>>>    self.sock.sendall(data)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1273, in sendall
>>>>    v = self.send(byte_view[count:])
>>>>        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1242, in send
>>>>    return self._sslobj.write(data)
>>>>           ^^^^^^^^^^^^^^^^^^^^^^^^
>>>>TimeoutError: The write operation timed out
2025-08-30 12:53:45 - visual_tool - INFO - middleware - dispatch:132 - [TraceID: 1756529611941_25ff9437] 请求完成: 200 - 耗时: 13.9s
2025-08-30 12:54:10 - visual_tool - INFO - middleware - dispatch:112 - [TraceID: 1756529650826_0e66ec73] 请求开始: POST /visual_tools/v1/visual_tool
2025-08-30 12:54:10 - visual_tool - INFO - processor - apply_video_tool:47 - [TraceID: 1756529650826_0e66ec73] video_download_start
2025-08-30 12:54:10 - visual_tool - INFO - download_util - download_to_tmp_path:48 - [TraceID: 1756529650826_0e66ec73] download_start: url=https://mcp-env.bj.bcebos.com/v1/tmp/ForBiggerJoyrides.mp4?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T04%3A43%3A42Z%2F-1%2Fhost%2F0f079e07d12fc923c0ad3038821969c4dd3f6ee8e8c580bb956b7858db2b629a, path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp_download_13dvabh0.mp4, suffix=.mp4
2025-08-30 12:54:11 - visual_tool - INFO - download_util - download_to_tmp_path:60 - [TraceID: 1756529650826_0e66ec73] download_success: size_kb=2317.21, attempts=1, elapsed_sec=0.848
2025-08-30 12:54:11 - visual_tool - INFO - processor - apply_video_tool:77 - [TraceID: 1756529650826_0e66ec73] video duration: 15.05
2025-08-30 12:54:12 - visual_tool - INFO - processor - apply_video_tool:86 - [TraceID: 1756529650826_0e66ec73] upload_invoke
2025-08-30 12:54:12 - visual_tool - INFO - upload_util - upload_file_to_bos:45 - [TraceID: 1756529650826_0e66ec73] upload_start: file_path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp_out_z8rli327.mp4, object_key=visual_tools/output/f77818abd11777eba54ff11b6dae8160_1756529652269_99484.mp4, size_kb=153.52
2025-08-30 12:54:12 - visual_tool - INFO - upload_util - upload_file_to_bos:57 - [TraceID: 1756529650826_0e66ec73] upload_success
2025-08-30 12:54:12 - visual_tool - INFO - processor - apply_video_tool:90 - [TraceID: 1756529650826_0e66ec73] video_processing_done
2025-08-30 12:54:12 - visual_tool - INFO - processor - apply_video_tool:140 - [TraceID: 1756529650826_0e66ec73] tmp_cleanup_ok
2025-08-30 12:54:12 - visual_tool - INFO - trace_util - log_request_response:151 - [TraceID: 1756529650826_0e66ec73] 请求数据: {'name': 'video_clip_tool', 'version': '', 'call_id': '', 'arguments': {'start_time': 1, 'end_time': 2}, 'extra_params': {'video_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/ForBiggerJoyrides.mp4?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T04%3A43%3A42Z%2F-1%2Fhost%2F0f079e07d12fc923c0ad3038821969c4dd3f6ee8e8c580bb956b7858db2b629a'}}
2025-08-30 12:54:12 - visual_tool - INFO - trace_util - log_request_response:158 - [TraceID: 1756529650826_0e66ec73] 响应数据: {'code': 0, 'message': 'Finish', 'data': '{"type": "video_url", "video_url": "https://mcp-env.bj.bcebos.com/visual_tools/output/f77818abd11777eba54ff11b6dae8160_1756529652269_99484.mp4?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T04%3A54%3A12Z%2F-1%2F%2Fc5462d1997cfd633f9e28201e554b12f6b37e5bbec34511802d9c0b68493d7c6"}'}
2025-08-30 12:54:12 - visual_tool - INFO - middleware - dispatch:132 - [TraceID: 1756529650826_0e66ec73] 请求完成: 200 - 耗时: 2.0s
2025-08-30 13:03:58 - visual_tool - INFO - middleware - dispatch:112 - [TraceID: 1756530238577_51a000be] 请求开始: POST /visual_tools/v1/visual_tool
2025-08-30 13:03:58 - visual_tool - INFO - processor - apply_video_tool:47 - [TraceID: 1756530238577_51a000be] video_download_start
2025-08-30 13:03:58 - visual_tool - INFO - download_util - download_to_tmp_path:48 - [TraceID: 1756530238577_51a000be] download_start: url=https://mcp-env.bj.bcebos.com/v1/tmp/ForBiggerJoyrides.mp4?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T04%3A43%3A42Z%2F-1%2Fhost%2F0f079e07d12fc923c0ad3038821969c4dd3f6ee8e8c580bb956b7858db2b629a, path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp_download_8yy0pssc.mp4, suffix=.mp4
2025-08-30 13:03:59 - visual_tool - INFO - download_util - download_to_tmp_path:60 - [TraceID: 1756530238577_51a000be] download_success: size_kb=2317.21, attempts=1, elapsed_sec=0.878
2025-08-30 13:04:00 - visual_tool - INFO - processor - apply_video_tool:77 - [TraceID: 1756530238577_51a000be] video duration: 15.05
2025-08-30 13:04:01 - visual_tool - INFO - processor - apply_video_tool:86 - [TraceID: 1756530238577_51a000be] upload_invoke
2025-08-30 13:04:01 - visual_tool - INFO - upload_util - upload_file_to_bos:45 - [TraceID: 1756530238577_51a000be] upload_start: file_path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp_out_apybvxtv.mp4, object_key=visual_tools/output/f77818abd11777eba54ff11b6dae8160_1756530241855_48259.mp4, size_kb=153.52
2025-08-30 13:04:02 - visual_tool - INFO - upload_util - upload_file_to_bos:57 - [TraceID: 1756530238577_51a000be] upload_success
2025-08-30 13:04:02 - visual_tool - INFO - processor - apply_video_tool:90 - [TraceID: 1756530238577_51a000be] video_processing_done
2025-08-30 13:04:02 - visual_tool - INFO - processor - apply_video_tool:140 - [TraceID: 1756530238577_51a000be] tmp_cleanup_ok
2025-08-30 13:04:02 - visual_tool - INFO - trace_util - log_request_response:151 - [TraceID: 1756530238577_51a000be] 请求数据: {'name': 'video_clip_tool', 'version': '', 'call_id': '', 'arguments': {'start_time': 1, 'end_time': 2}, 'extra_params': {'video_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/ForBiggerJoyrides.mp4?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T04%3A43%3A42Z%2F-1%2Fhost%2F0f079e07d12fc923c0ad3038821969c4dd3f6ee8e8c580bb956b7858db2b629a'}}
2025-08-30 13:04:02 - visual_tool - INFO - trace_util - log_request_response:158 - [TraceID: 1756530238577_51a000be] 响应数据: {'code': 0, 'message': 'Finish', 'data': '{"type": "video_url", "video_url": "https://mcp-env.bj.bcebos.com/visual_tools/output/f77818abd11777eba54ff11b6dae8160_1756530241855_48259.mp4?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T05%3A04%3A02Z%2F-1%2F%2Fb59740b35887f93f430e98f472fb22413348c51210c65b0d1933d1520ea4b375"}'}
2025-08-30 13:04:02 - visual_tool - INFO - middleware - dispatch:132 - [TraceID: 1756530238577_51a000be] 请求完成: 200 - 耗时: 3.6s
2025-08-30 13:07:04 - visual_tool - INFO - middleware - dispatch:112 - [TraceID: 1756530424891_8cd6ca11] 请求开始: POST /visual_tools/v1/visual_tool
2025-08-30 13:07:04 - visual_tool - INFO - processor - apply_video_tool:47 - [TraceID: 1756530424891_8cd6ca11] video_download_start
2025-08-30 13:07:04 - visual_tool - INFO - download_util - download_to_tmp_path:48 - [TraceID: 1756530424891_8cd6ca11] download_start: url=https://mcp-env.bj.bcebos.com/v1/tmp/ForBiggerJoyrides.mp4?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T04%3A43%3A42Z%2F-1%2Fhost%2F0f079e07d12fc923c0ad3038821969c4dd3f6ee8e8c580bb956b7858db2b629a, path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp_download_klc9bpsm.mp4, suffix=.mp4
2025-08-30 13:07:05 - visual_tool - INFO - download_util - download_to_tmp_path:60 - [TraceID: 1756530424891_8cd6ca11] download_success: size_kb=2317.21, attempts=1, elapsed_sec=0.56
2025-08-30 13:07:05 - visual_tool - INFO - processor - apply_video_tool:77 - [TraceID: 1756530424891_8cd6ca11] video duration: 15.05
2025-08-30 13:07:05 - visual_tool - INFO - processor - apply_video_tool:86 - [TraceID: 1756530424891_8cd6ca11] upload_invoke
2025-08-30 13:07:05 - visual_tool - INFO - upload_util - upload_file_to_bos:45 - [TraceID: 1756530424891_8cd6ca11] upload_start: file_path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp_out_2t_6zxef.mp4, object_key=visual_tools/output/f77818abd11777eba54ff11b6dae8160_1756530425969_33118.mp4, size_kb=153.52
2025-08-30 13:07:06 - visual_tool - INFO - upload_util - upload_file_to_bos:57 - [TraceID: 1756530424891_8cd6ca11] upload_success
2025-08-30 13:07:06 - visual_tool - INFO - processor - apply_video_tool:90 - [TraceID: 1756530424891_8cd6ca11] video_processing_done
2025-08-30 13:07:06 - visual_tool - INFO - processor - apply_video_tool:140 - [TraceID: 1756530424891_8cd6ca11] tmp_cleanup_ok
2025-08-30 13:07:06 - visual_tool - INFO - trace_util - log_request_response:151 - [TraceID: 1756530424891_8cd6ca11] 请求数据: {'name': 'video_clip_tool', 'version': '', 'call_id': '', 'arguments': {'start_time': 1, 'end_time': 2}, 'extra_params': {'video_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/ForBiggerJoyrides.mp4?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T04%3A43%3A42Z%2F-1%2Fhost%2F0f079e07d12fc923c0ad3038821969c4dd3f6ee8e8c580bb956b7858db2b629a'}}
2025-08-30 13:07:06 - visual_tool - INFO - trace_util - log_request_response:158 - [TraceID: 1756530424891_8cd6ca11] 响应数据: {'code': 0, 'message': 'Finish', 'data': '{"type": "video_url", "video_url": "https://mcp-env.bj.bcebos.com/visual_tools/output/f77818abd11777eba54ff11b6dae8160_1756530425969_33118.mp4?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T05%3A07%3A06Z%2F-1%2F%2F493faf031775d4c2e7efd67b49e3731842989beaa112b49fbfecf6b586455868"}'}
2025-08-30 13:07:06 - visual_tool - INFO - middleware - dispatch:132 - [TraceID: 1756530424891_8cd6ca11] 请求完成: 200 - 耗时: 1.6s
2025-08-30 13:08:02 - visual_tool - INFO - middleware - dispatch:112 - [TraceID: 1756530482403_0d5d7843] 请求开始: POST /visual_tools/v1/visual_tool
2025-08-30 13:08:02 - visual_tool - INFO - processor - apply_video_tool:47 - [TraceID: 1756530482403_0d5d7843] video_download_start
2025-08-30 13:08:02 - visual_tool - INFO - download_util - download_to_tmp_path:48 - [TraceID: 1756530482403_0d5d7843] download_start: url=https://mcp-env.bj.bcebos.com/v1/tmp/ForBiggerJoyrides.mp4?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T04%3A43%3A42Z%2F-1%2Fhost%2F0f079e07d12fc923c0ad3038821969c4dd3f6ee8e8c580bb956b7858db2b629a, path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp_download_sk4h5q62.mp4, suffix=.mp4
2025-08-30 13:08:02 - visual_tool - INFO - download_util - download_to_tmp_path:60 - [TraceID: 1756530482403_0d5d7843] download_success: size_kb=2317.21, attempts=1, elapsed_sec=0.518
2025-08-30 13:08:02 - visual_tool - INFO - processor - apply_video_tool:107 - [TraceID: 1756530482403_0d5d7843] video_processing_start
2025-08-30 13:08:03 - visual_tool - INFO - processor - apply_video_tool:116 - [TraceID: 1756530482403_0d5d7843] upload_invoke: frame_idx=1, size_kb=674.61, frame_path=./tmp/2025-08-30/_tmp_frame_1.png
2025-08-30 13:08:03 - visual_tool - INFO - upload_util - upload_file_to_bos:45 - [TraceID: 1756530482403_0d5d7843] upload_start: file_path=./tmp/2025-08-30/_tmp_frame_1.png, object_key=visual_tools/output/9f118c7bd7bed25edf90eafaa8256f09_1756530483012_67488.png, size_kb=674.61
2025-08-30 13:08:04 - visual_tool - INFO - upload_util - upload_file_to_bos:57 - [TraceID: 1756530482403_0d5d7843] upload_success
2025-08-30 13:08:04 - visual_tool - INFO - processor - apply_video_tool:125 - [TraceID: 1756530482403_0d5d7843] tmp_cleanup_ok
2025-08-30 13:08:04 - visual_tool - INFO - processor - apply_video_tool:116 - [TraceID: 1756530482403_0d5d7843] upload_invoke: frame_idx=2, size_kb=698.12, frame_path=./tmp/2025-08-30/_tmp_frame_2.png
2025-08-30 13:08:04 - visual_tool - INFO - upload_util - upload_file_to_bos:45 - [TraceID: 1756530482403_0d5d7843] upload_start: file_path=./tmp/2025-08-30/_tmp_frame_2.png, object_key=visual_tools/output/420fc41c61c4248e0a9af2fa311f7494_1756530484380_70226.png, size_kb=698.12
2025-08-30 13:08:05 - visual_tool - INFO - upload_util - upload_file_to_bos:57 - [TraceID: 1756530482403_0d5d7843] upload_success
2025-08-30 13:08:05 - visual_tool - INFO - processor - apply_video_tool:125 - [TraceID: 1756530482403_0d5d7843] tmp_cleanup_ok
2025-08-30 13:08:05 - visual_tool - INFO - processor - apply_video_tool:116 - [TraceID: 1756530482403_0d5d7843] upload_invoke: frame_idx=3, size_kb=717.43, frame_path=./tmp/2025-08-30/_tmp_frame_3.png
2025-08-30 13:08:05 - visual_tool - INFO - upload_util - upload_file_to_bos:45 - [TraceID: 1756530482403_0d5d7843] upload_start: file_path=./tmp/2025-08-30/_tmp_frame_3.png, object_key=visual_tools/output/b3dd6ef98014ea567795ddefb2af892c_1756530485571_61364.png, size_kb=717.43
2025-08-30 13:08:09 - visual_tool - INFO - upload_util - upload_file_to_bos:57 - [TraceID: 1756530482403_0d5d7843] upload_success
2025-08-30 13:08:09 - visual_tool - INFO - processor - apply_video_tool:125 - [TraceID: 1756530482403_0d5d7843] tmp_cleanup_ok
2025-08-30 13:08:09 - visual_tool - INFO - processor - apply_video_tool:132 - [TraceID: 1756530482403_0d5d7843] video_processing_done
2025-08-30 13:08:09 - visual_tool - INFO - processor - apply_video_tool:140 - [TraceID: 1756530482403_0d5d7843] tmp_cleanup_ok
2025-08-30 13:08:09 - visual_tool - INFO - trace_util - log_request_response:151 - [TraceID: 1756530482403_0d5d7843] 请求数据: {'name': 'video_select_frames_tool', 'version': '', 'call_id': '', 'arguments': {'target_frame': [1, 2, 3]}, 'extra_params': {'video_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/ForBiggerJoyrides.mp4?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T04%3A43%3A42Z%2F-1%2Fhost%2F0f079e07d12fc923c0ad3038821969c4dd3f6ee8e8c580bb956b7858db2b629a'}}
2025-08-30 13:08:09 - visual_tool - INFO - trace_util - log_request_response:158 - [TraceID: 1756530482403_0d5d7843] 响应数据: {'code': 0, 'message': 'Finish', 'data': '{"type": "image_url_list", "image_url_list": ["https://mcp-env.bj.bcebos.com/visual_tools/output/9f118c7bd7bed25edf90eafaa8256f09_1756530483012_67488.png?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T05%3A08%3A04Z%2F-1%2F%2F515f70cc1adf9ff4ac5dfbe04b7aeaa363fac3dcc919190d214924268b10e7de", "https://mcp-env.bj.bcebos.com/visual_tools/output/420fc41c61c4248e0a9af2fa311f7494_1756530484380_70226.png?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T05%3A08%3A05Z%2F-1%2F%2F70268abd7ff14b3a4e30448f202d73e5f7fc04641ac392af147285ab4be3e2dc", "https://mcp-env.bj.bcebos.com/visual_tools/output/b3dd6ef98014ea567795ddefb2af892c_1756530485571_61364.png?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T05%3A08%3A09Z%2F-1%2F%2F099b308b7dc4bd753847b3f62aeaf31e08365e884372872f6f9cb6b7e243c036"]}'}
2025-08-30 13:08:09 - visual_tool - INFO - middleware - dispatch:132 - [TraceID: 1756530482403_0d5d7843] 请求完成: 200 - 耗时: 7.2s
2025-08-30 13:08:44 - visual_tool - INFO - middleware - dispatch:112 - [TraceID: 1756530524767_a512494b] 请求开始: POST /visual_tools/v1/visual_tool
2025-08-30 13:08:44 - visual_tool - INFO - processor - apply_video_tool:47 - [TraceID: 1756530524767_a512494b] video_download_start
2025-08-30 13:08:44 - visual_tool - INFO - download_util - download_to_tmp_path:48 - [TraceID: 1756530524767_a512494b] download_start: url=https://mcp-env.bj.bcebos.com/v1/tmp/ForBiggerJoyrides.mp4?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T04%3A43%3A42Z%2F-1%2Fhost%2F0f079e07d12fc923c0ad3038821969c4dd3f6ee8e8c580bb956b7858db2b629a, path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp_download_hzxg6cud.mp4, suffix=.mp4
2025-08-30 13:08:45 - visual_tool - INFO - download_util - download_to_tmp_path:60 - [TraceID: 1756530524767_a512494b] download_success: size_kb=2317.21, attempts=1, elapsed_sec=0.591
2025-08-30 13:08:45 - visual_tool - INFO - processor - apply_video_tool:107 - [TraceID: 1756530524767_a512494b] video_processing_start
2025-08-30 13:08:45 - visual_tool - INFO - processor - apply_video_tool:116 - [TraceID: 1756530524767_a512494b] upload_invoke: frame_idx=2, size_kb=698.12, frame_path=./tmp/2025-08-30/_tmp_frame_2.png
2025-08-30 13:08:45 - visual_tool - INFO - upload_util - upload_file_to_bos:45 - [TraceID: 1756530524767_a512494b] upload_start: file_path=./tmp/2025-08-30/_tmp_frame_2.png, object_key=visual_tools/output/420fc41c61c4248e0a9af2fa311f7494_1756530525462_80641.png, size_kb=698.12
2025-08-30 13:08:47 - visual_tool - INFO - upload_util - upload_file_to_bos:57 - [TraceID: 1756530524767_a512494b] upload_success
2025-08-30 13:08:47 - visual_tool - INFO - processor - apply_video_tool:125 - [TraceID: 1756530524767_a512494b] tmp_cleanup_ok
2025-08-30 13:08:47 - visual_tool - INFO - processor - apply_video_tool:116 - [TraceID: 1756530524767_a512494b] upload_invoke: frame_idx=3, size_kb=717.43, frame_path=./tmp/2025-08-30/_tmp_frame_3.png
2025-08-30 13:08:47 - visual_tool - INFO - upload_util - upload_file_to_bos:45 - [TraceID: 1756530524767_a512494b] upload_start: file_path=./tmp/2025-08-30/_tmp_frame_3.png, object_key=visual_tools/output/b3dd6ef98014ea567795ddefb2af892c_1756530527999_13462.png, size_kb=717.43
2025-08-30 13:08:48 - visual_tool - INFO - upload_util - upload_file_to_bos:57 - [TraceID: 1756530524767_a512494b] upload_success
2025-08-30 13:08:48 - visual_tool - INFO - processor - apply_video_tool:125 - [TraceID: 1756530524767_a512494b] tmp_cleanup_ok
2025-08-30 13:08:48 - visual_tool - INFO - processor - apply_video_tool:116 - [TraceID: 1756530524767_a512494b] upload_invoke: frame_idx=100, size_kb=798.83, frame_path=./tmp/2025-08-30/_tmp_frame_100.png
2025-08-30 13:08:48 - visual_tool - INFO - upload_util - upload_file_to_bos:45 - [TraceID: 1756530524767_a512494b] upload_start: file_path=./tmp/2025-08-30/_tmp_frame_100.png, object_key=visual_tools/output/a140cc23bea4e55eec054046424cabdf_1756530528692_96308.png, size_kb=798.83
2025-08-30 13:08:49 - visual_tool - INFO - upload_util - upload_file_to_bos:57 - [TraceID: 1756530524767_a512494b] upload_success
2025-08-30 13:08:49 - visual_tool - INFO - processor - apply_video_tool:125 - [TraceID: 1756530524767_a512494b] tmp_cleanup_ok
2025-08-30 13:08:50 - visual_tool - INFO - processor - apply_video_tool:132 - [TraceID: 1756530524767_a512494b] video_processing_done
2025-08-30 13:08:50 - visual_tool - INFO - processor - apply_video_tool:140 - [TraceID: 1756530524767_a512494b] tmp_cleanup_ok
2025-08-30 13:08:50 - visual_tool - INFO - trace_util - log_request_response:151 - [TraceID: 1756530524767_a512494b] 请求数据: {'name': 'video_select_frames_tool', 'version': '', 'call_id': '', 'arguments': {'target_frame': [100, 2, 3]}, 'extra_params': {'video_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/ForBiggerJoyrides.mp4?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T04%3A43%3A42Z%2F-1%2Fhost%2F0f079e07d12fc923c0ad3038821969c4dd3f6ee8e8c580bb956b7858db2b629a'}}
2025-08-30 13:08:50 - visual_tool - INFO - trace_util - log_request_response:158 - [TraceID: 1756530524767_a512494b] 响应数据: {'code': 0, 'message': 'Finish', 'data': '{"type": "image_url_list", "image_url_list": ["https://mcp-env.bj.bcebos.com/visual_tools/output/420fc41c61c4248e0a9af2fa311f7494_1756530525462_80641.png?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T05%3A08%3A47Z%2F-1%2F%2F6cf3828fef0990e845a713daa9103089deb99d143ade14ad3869658120b7db8e", "https://mcp-env.bj.bcebos.com/visual_tools/output/b3dd6ef98014ea567795ddefb2af892c_1756530527999_13462.png?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T05%3A08%3A48Z%2F-1%2F%2Fa6c290e54fe8e6e2b9e802da37e6b7baf2b0c74ff09356a298890fb22064d50b", "https://mcp-env.bj.bcebos.com/visual_tools/output/a140cc23bea4e55eec054046424cabdf_1756530528692_96308.png?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T05%3A08%3A49Z%2F-1%2F%2F6c8c5e5cfe62dcc5d691b2f516322ec3063d28e0427020342e1102a1d3166b5a"]}'}
2025-08-30 13:08:50 - visual_tool - INFO - middleware - dispatch:132 - [TraceID: 1756530524767_a512494b] 请求完成: 200 - 耗时: 5.4s
2025-08-30 13:12:14 - visual_tool - INFO - middleware - dispatch:112 - [TraceID: 1756530734452_c9879cfc] 请求开始: POST /visual_tools/v1/visual_tool
2025-08-30 13:12:14 - visual_tool - INFO - processor - apply_video_tool:47 - [TraceID: 1756530734452_c9879cfc] video_download_start
2025-08-30 13:12:14 - visual_tool - INFO - download_util - download_to_tmp_path:48 - [TraceID: 1756530734452_c9879cfc] download_start: url=https://mcp-env.bj.bcebos.com/v1/tmp/ForBiggerJoyrides.mp4?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T04%3A43%3A42Z%2F-1%2Fhost%2F0f079e07d12fc923c0ad3038821969c4dd3f6ee8e8c580bb956b7858db2b629a, path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp_download_5hbgre0c.mp4, suffix=.mp4
2025-08-30 13:12:15 - visual_tool - INFO - download_util - download_to_tmp_path:60 - [TraceID: 1756530734452_c9879cfc] download_success: size_kb=2317.21, attempts=1, elapsed_sec=0.895
2025-08-30 13:12:15 - visual_tool - INFO - processor - apply_video_tool:107 - [TraceID: 1756530734452_c9879cfc] video_processing_start
2025-08-30 13:12:15 - visual_tool - INFO - processor - apply_video_tool:116 - [TraceID: 1756530734452_c9879cfc] upload_invoke: frame_idx=2, size_kb=698.12, frame_path=./tmp/2025-08-30/_tmp_frame_2.png
2025-08-30 13:12:15 - visual_tool - INFO - upload_util - upload_file_to_bos:45 - [TraceID: 1756530734452_c9879cfc] upload_start: file_path=./tmp/2025-08-30/_tmp_frame_2.png, object_key=visual_tools/output/420fc41c61c4248e0a9af2fa311f7494_1756530735432_26556.png, size_kb=698.12
2025-08-30 13:12:16 - visual_tool - INFO - upload_util - upload_file_to_bos:57 - [TraceID: 1756530734452_c9879cfc] upload_success
2025-08-30 13:12:16 - visual_tool - INFO - processor - apply_video_tool:125 - [TraceID: 1756530734452_c9879cfc] tmp_cleanup_ok
2025-08-30 13:12:17 - visual_tool - INFO - processor - apply_video_tool:116 - [TraceID: 1756530734452_c9879cfc] upload_invoke: frame_idx=3, size_kb=717.43, frame_path=./tmp/2025-08-30/_tmp_frame_3.png
2025-08-30 13:12:17 - visual_tool - INFO - upload_util - upload_file_to_bos:45 - [TraceID: 1756530734452_c9879cfc] upload_start: file_path=./tmp/2025-08-30/_tmp_frame_3.png, object_key=visual_tools/output/b3dd6ef98014ea567795ddefb2af892c_1756530737009_90386.png, size_kb=717.43
2025-08-30 13:12:18 - visual_tool - INFO - upload_util - upload_file_to_bos:57 - [TraceID: 1756530734452_c9879cfc] upload_success
2025-08-30 13:12:18 - visual_tool - INFO - processor - apply_video_tool:125 - [TraceID: 1756530734452_c9879cfc] tmp_cleanup_ok
2025-08-30 13:12:18 - visual_tool - INFO - processor - apply_video_tool:132 - [TraceID: 1756530734452_c9879cfc] video_processing_done
2025-08-30 13:12:18 - visual_tool - INFO - processor - apply_video_tool:140 - [TraceID: 1756530734452_c9879cfc] tmp_cleanup_ok
2025-08-30 13:12:18 - visual_tool - INFO - trace_util - log_request_response:151 - [TraceID: 1756530734452_c9879cfc] 请求数据: {'name': 'video_select_frames_tool', 'version': '', 'call_id': '', 'arguments': {'target_frame': [10000, 2, 3]}, 'extra_params': {'video_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/ForBiggerJoyrides.mp4?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T04%3A43%3A42Z%2F-1%2Fhost%2F0f079e07d12fc923c0ad3038821969c4dd3f6ee8e8c580bb956b7858db2b629a'}}
2025-08-30 13:12:18 - visual_tool - INFO - trace_util - log_request_response:158 - [TraceID: 1756530734452_c9879cfc] 响应数据: {'code': 0, 'message': 'Finish', 'data': '{"type": "image_url_list", "image_url_list": ["https://mcp-env.bj.bcebos.com/visual_tools/output/420fc41c61c4248e0a9af2fa311f7494_1756530735432_26556.png?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T05%3A12%3A16Z%2F-1%2F%2Fc465d94832b89394379ddccc434166fe1e90871ad0ee4559f1a5d2e9b609a4fb", "https://mcp-env.bj.bcebos.com/visual_tools/output/b3dd6ef98014ea567795ddefb2af892c_1756530737009_90386.png?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T05%3A12%3A18Z%2F-1%2F%2F25ba8d28d5d1c60b20021e92cfa00b451de7ea17ebcc1e1564956372ec0b0288"]}'}
2025-08-30 13:12:18 - visual_tool - INFO - middleware - dispatch:132 - [TraceID: 1756530734452_c9879cfc] 请求完成: 200 - 耗时: 4.3s
2025-08-30 13:12:51 - visual_tool - INFO - middleware - dispatch:112 - [TraceID: 1756530771083_9ce269f4] 请求开始: POST /visual_tools/v1/visual_tool
2025-08-30 13:12:51 - visual_tool - INFO - processor - apply_video_tool:47 - [TraceID: 1756530771083_9ce269f4] video_download_start
2025-08-30 13:12:51 - visual_tool - INFO - download_util - download_to_tmp_path:48 - [TraceID: 1756530771083_9ce269f4] download_start: url=https://mcp-env.bj.bcebos.com/v1/tmp/ForBiggerJoyrides.mp4?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T04%3A43%3A42Z%2F-1%2Fhost%2F0f079e07d12fc923c0ad3038821969c4dd3f6ee8e8c580bb956b7858db2b629a, path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp_download_1vkb9tsm.mp4, suffix=.mp4
2025-08-30 13:12:52 - visual_tool - INFO - download_util - download_to_tmp_path:60 - [TraceID: 1756530771083_9ce269f4] download_success: size_kb=2317.21, attempts=1, elapsed_sec=0.917
2025-08-30 13:12:52 - visual_tool - INFO - processor - apply_video_tool:77 - [TraceID: 1756530771083_9ce269f4] video duration: 15.05
2025-08-30 13:12:52 - visual_tool - INFO - processor - apply_video_tool:86 - [TraceID: 1756530771083_9ce269f4] upload_invoke
2025-08-30 13:12:52 - visual_tool - INFO - upload_util - upload_file_to_bos:45 - [TraceID: 1756530771083_9ce269f4] upload_start: file_path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp_out_j5nttpry.mp4, object_key=visual_tools/output/f77818abd11777eba54ff11b6dae8160_1756530772556_94909.mp4, size_kb=153.52
2025-08-30 13:12:53 - visual_tool - INFO - upload_util - upload_file_to_bos:57 - [TraceID: 1756530771083_9ce269f4] upload_success
2025-08-30 13:12:53 - visual_tool - INFO - processor - apply_video_tool:90 - [TraceID: 1756530771083_9ce269f4] video_processing_done
2025-08-30 13:12:53 - visual_tool - INFO - processor - apply_video_tool:140 - [TraceID: 1756530771083_9ce269f4] tmp_cleanup_ok
2025-08-30 13:12:53 - visual_tool - INFO - trace_util - log_request_response:151 - [TraceID: 1756530771083_9ce269f4] 请求数据: {'name': 'video_clip_tool', 'version': '', 'call_id': '', 'arguments': {'start_time': 1, 'end_time': 2}, 'extra_params': {'video_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/ForBiggerJoyrides.mp4?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T04%3A43%3A42Z%2F-1%2Fhost%2F0f079e07d12fc923c0ad3038821969c4dd3f6ee8e8c580bb956b7858db2b629a'}}
2025-08-30 13:12:53 - visual_tool - INFO - trace_util - log_request_response:158 - [TraceID: 1756530771083_9ce269f4] 响应数据: {'code': 0, 'message': 'Finish', 'data': '{"type": "video_url", "video_url": "https://mcp-env.bj.bcebos.com/visual_tools/output/f77818abd11777eba54ff11b6dae8160_1756530772556_94909.mp4?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T05%3A12%3A53Z%2F-1%2F%2F1f43bf448c61aa82047baef72a306e2d22fde91bf9313930af123a1918d10b72"}'}
2025-08-30 13:12:53 - visual_tool - INFO - middleware - dispatch:132 - [TraceID: 1756530771083_9ce269f4] 请求完成: 200 - 耗时: 2.0s
2025-08-30 13:13:23 - visual_tool - INFO - middleware - dispatch:112 - [TraceID: 1756530803168_f4fc984f] 请求开始: POST /visual_tools/v1/visual_tool
2025-08-30 13:13:23 - visual_tool - INFO - processor - apply_video_tool:47 - [TraceID: 1756530803168_f4fc984f] video_download_start
2025-08-30 13:13:23 - visual_tool - INFO - download_util - download_to_tmp_path:48 - [TraceID: 1756530803168_f4fc984f] download_start: url=https://mcp-env.bj.bcebos.com/v1/tmp/ForBiggerJoyrides.mp4?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T04%3A43%3A42Z%2F-1%2Fhost%2F0f079e07d12fc923c0ad3038821969c4dd3f6ee8e8c580bb956b7858db2b629a, path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp_download_omdtt1jy.mp4, suffix=.mp4
2025-08-30 13:13:23 - visual_tool - INFO - download_util - download_to_tmp_path:60 - [TraceID: 1756530803168_f4fc984f] download_success: size_kb=2317.21, attempts=1, elapsed_sec=0.552
2025-08-30 13:13:23 - visual_tool - INFO - processor - apply_video_tool:77 - [TraceID: 1756530803168_f4fc984f] video duration: 15.05
2025-08-30 13:13:26 - visual_tool - INFO - processor - apply_video_tool:86 - [TraceID: 1756530803168_f4fc984f] upload_invoke
2025-08-30 13:13:26 - visual_tool - INFO - upload_util - upload_file_to_bos:45 - [TraceID: 1756530803168_f4fc984f] upload_start: file_path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp_out_n5o89qdn.mp4, object_key=visual_tools/output/4d6b3e095d6299053586db3f9b8e5c9c_1756530806379_72318.mp4, size_kb=2001.05
2025-08-30 13:13:36 - visual_tool - ERROR - upload_util - upload_file_to_bos:60 - [TraceID: 1756530803168_f4fc984f] upload_failed
2025-08-30 13:13:36 - visual_tool - INFO - processor - apply_video_tool:140 - [TraceID: 1756530803168_f4fc984f] tmp_cleanup_ok
2025-08-30 13:13:36 - visual_tool - INFO - trace_util - log_request_response:151 - [TraceID: 1756530803168_f4fc984f] 请求数据: {'name': 'video_clip_tool', 'version': '', 'call_id': '', 'arguments': {'start_time': 1, 'end_time': 15}, 'extra_params': {'video_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/ForBiggerJoyrides.mp4?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T04%3A43%3A42Z%2F-1%2Fhost%2F0f079e07d12fc923c0ad3038821969c4dd3f6ee8e8c580bb956b7858db2b629a'}}
2025-08-30 13:13:36 - visual_tool - ERROR - trace_util - log_request_response:155 - [TraceID: 1756530803168_f4fc984f] 请求处理失败: Unable to execute HTTP request. Retried 3 times. All trace backs:
>>>>Traceback (most recent call last):
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 210, in send_request
>>>>    http_response = _send_http_request(
>>>>                    ^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 101, in _send_http_request
>>>>    conn.send(buf)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/http/client.py", line 1019, in send
>>>>    self.sock.sendall(data)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1273, in sendall
>>>>    v = self.send(byte_view[count:])
>>>>        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1242, in send
>>>>    return self._sslobj.write(data)
>>>>           ^^^^^^^^^^^^^^^^^^^^^^^^
>>>>TimeoutError: The write operation timed out
>>>>Traceback (most recent call last):
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 210, in send_request
>>>>    http_response = _send_http_request(
>>>>                    ^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 101, in _send_http_request
>>>>    conn.send(buf)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/http/client.py", line 1019, in send
>>>>    self.sock.sendall(data)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1273, in sendall
>>>>    v = self.send(byte_view[count:])
>>>>        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1242, in send
>>>>    return self._sslobj.write(data)
>>>>           ^^^^^^^^^^^^^^^^^^^^^^^^
>>>>TimeoutError: The write operation timed out
>>>>Traceback (most recent call last):
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 210, in send_request
>>>>    http_response = _send_http_request(
>>>>                    ^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 101, in _send_http_request
>>>>    conn.send(buf)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/http/client.py", line 1019, in send
>>>>    self.sock.sendall(data)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1273, in sendall
>>>>    v = self.send(byte_view[count:])
>>>>        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1242, in send
>>>>    return self._sslobj.write(data)
>>>>           ^^^^^^^^^^^^^^^^^^^^^^^^
>>>>TimeoutError: The write operation timed out
>>>>Traceback (most recent call last):
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 210, in send_request
>>>>    http_response = _send_http_request(
>>>>                    ^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 101, in _send_http_request
>>>>    conn.send(buf)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/http/client.py", line 1019, in send
>>>>    self.sock.sendall(data)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1273, in sendall
>>>>    v = self.send(byte_view[count:])
>>>>        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1242, in send
>>>>    return self._sslobj.write(data)
>>>>           ^^^^^^^^^^^^^^^^^^^^^^^^
>>>>TimeoutError: The write operation timed out
2025-08-30 13:13:36 - visual_tool - INFO - middleware - dispatch:132 - [TraceID: 1756530803168_f4fc984f] 请求完成: 200 - 耗时: 13.8s
2025-08-30 13:24:01 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-30 13:24:02 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-30 13:24:02 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-30 13:24:02 - uvicorn.error - INFO - server - _serve:94 - Finished server process [94526]
2025-08-30 13:24:04 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-08-30 13:24:04 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-08-30 13:24:04 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-08-30 13:24:04 - uvicorn.error - INFO - server - _serve:84 - Started server process [99220]
2025-08-30 13:24:04 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-30 13:24:04 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-30 13:24:04 - uvicorn.error - INFO - server - _log_started_message:216 - Uvicorn running on http://0.0.0.0:8080 (Press CTRL+C to quit)
2025-08-30 13:24:13 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-30 13:24:14 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-30 13:24:14 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-30 13:24:14 - uvicorn.error - INFO - server - _serve:94 - Finished server process [99220]
2025-08-30 13:24:15 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-08-30 13:24:15 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-08-30 13:24:15 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-08-30 13:24:15 - uvicorn.error - INFO - server - _serve:84 - Started server process [99507]
2025-08-30 13:24:15 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-08-30 13:24:15 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-08-30 13:24:15 - uvicorn.error - INFO - server - _log_started_message:216 - Uvicorn running on http://0.0.0.0:8080 (Press CTRL+C to quit)
2025-08-30 13:24:19 - visual_tool - INFO - middleware - dispatch:112 - [TraceID: 1756531459279_16749ebe] 请求开始: POST /visual_tools/v1/visual_tool
2025-08-30 13:24:19 - visual_tool - INFO - processor - apply_video_tool:47 - [TraceID: 1756531459279_16749ebe] video_download_start
2025-08-30 13:24:19 - visual_tool - INFO - download_util - download_to_tmp_path:48 - [TraceID: 1756531459279_16749ebe] download_start: url=https://mcp-env.bj.bcebos.com/v1/tmp/ForBiggerJoyrides.mp4?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T04%3A43%3A42Z%2F-1%2Fhost%2F0f079e07d12fc923c0ad3038821969c4dd3f6ee8e8c580bb956b7858db2b629a, path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp_download_6lt_hdq2.mp4, suffix=.mp4
2025-08-30 13:24:19 - visual_tool - INFO - download_util - download_to_tmp_path:60 - [TraceID: 1756531459279_16749ebe] download_success: size_kb=2317.21, attempts=1, elapsed_sec=0.532
2025-08-30 13:24:20 - visual_tool - INFO - processor - apply_video_tool:77 - [TraceID: 1756531459279_16749ebe] video duration: 15.05
2025-08-30 13:24:23 - visual_tool - INFO - processor - apply_video_tool:86 - [TraceID: 1756531459279_16749ebe] upload_invoke
2025-08-30 13:24:23 - visual_tool - INFO - upload_util - upload_file_to_bos:45 - [TraceID: 1756531459279_16749ebe] upload_start: file_path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp_out_lhi4z88w.mp4, object_key=visual_tools/output/4d6b3e095d6299053586db3f9b8e5c9c_1756531463005_81284.mp4, size_kb=2001.05
2025-08-30 13:24:23 - visual_tool - INFO - bos_util - get_bos_client:51 - Initializing BOS client...
2025-08-30 13:24:23 - visual_tool - INFO - bos_util - get_bos_client:62 - BOS client initialized successfully
2025-08-30 13:24:32 - visual_tool - INFO - upload_util - upload_file_to_bos:57 - [TraceID: 1756531459279_16749ebe] upload_success
2025-08-30 13:24:32 - visual_tool - INFO - processor - apply_video_tool:90 - [TraceID: 1756531459279_16749ebe] video_processing_done
2025-08-30 13:24:32 - visual_tool - INFO - processor - apply_video_tool:140 - [TraceID: 1756531459279_16749ebe] tmp_cleanup_ok
2025-08-30 13:24:32 - visual_tool - INFO - trace_util - log_request_response:151 - [TraceID: 1756531459279_16749ebe] 请求数据: {'name': 'video_clip_tool', 'version': '', 'call_id': '', 'arguments': {'start_time': 1, 'end_time': 15}, 'extra_params': {'video_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/ForBiggerJoyrides.mp4?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T04%3A43%3A42Z%2F-1%2Fhost%2F0f079e07d12fc923c0ad3038821969c4dd3f6ee8e8c580bb956b7858db2b629a'}}
2025-08-30 13:24:32 - visual_tool - INFO - trace_util - log_request_response:158 - [TraceID: 1756531459279_16749ebe] 响应数据: {'code': 0, 'message': 'Finish', 'data': '{"type": "video_url", "video_url": "https://mcp-env.bj.bcebos.com/visual_tools/output/4d6b3e095d6299053586db3f9b8e5c9c_1756531463005_81284.mp4?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T05%3A24%3A32Z%2F-1%2F%2F71284a4ebbf42a3b331debb0bc3210726c6d874c9299702846a062eb68d645ea"}'}
2025-08-30 13:24:32 - visual_tool - INFO - middleware - dispatch:132 - [TraceID: 1756531459279_16749ebe] 请求完成: 200 - 耗时: 13.3s
2025-08-30 13:24:49 - visual_tool - INFO - middleware - dispatch:112 - [TraceID: 1756531489283_e09592c6] 请求开始: POST /visual_tools/v1/visual_tool
2025-08-30 13:24:49 - visual_tool - INFO - processor - apply_video_tool:47 - [TraceID: 1756531489283_e09592c6] video_download_start
2025-08-30 13:24:49 - visual_tool - INFO - download_util - download_to_tmp_path:48 - [TraceID: 1756531489283_e09592c6] download_start: url=https://mcp-env.bj.bcebos.com/v1/tmp/ForBiggerJoyrides.mp4?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T04%3A43%3A42Z%2F-1%2Fhost%2F0f079e07d12fc923c0ad3038821969c4dd3f6ee8e8c580bb956b7858db2b629a, path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp_download_kn0buth6.mp4, suffix=.mp4
2025-08-30 13:24:49 - visual_tool - INFO - download_util - download_to_tmp_path:60 - [TraceID: 1756531489283_e09592c6] download_success: size_kb=2317.21, attempts=1, elapsed_sec=0.487
2025-08-30 13:24:49 - visual_tool - INFO - processor - apply_video_tool:77 - [TraceID: 1756531489283_e09592c6] video duration: 15.05
2025-08-30 13:24:50 - visual_tool - INFO - processor - apply_video_tool:140 - [TraceID: 1756531489283_e09592c6] tmp_cleanup_ok
2025-08-30 13:24:50 - visual_tool - INFO - trace_util - log_request_response:151 - [TraceID: 1756531489283_e09592c6] 请求数据: {'name': 'video_clip_tool', 'version': '', 'call_id': '', 'arguments': {'start_time': 1, 'end_time': 30}, 'extra_params': {'video_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/ForBiggerJoyrides.mp4?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T04%3A43%3A42Z%2F-1%2Fhost%2F0f079e07d12fc923c0ad3038821969c4dd3f6ee8e8c580bb956b7858db2b629a'}}
2025-08-30 13:24:50 - visual_tool - ERROR - trace_util - log_request_response:155 - [TraceID: 1756531489283_e09592c6] 请求处理失败: end_time (30.00) should be smaller or equal to the clip's duration (15.05).
2025-08-30 13:24:50 - visual_tool - INFO - middleware - dispatch:132 - [TraceID: 1756531489283_e09592c6] 请求完成: 200 - 耗时: 745ms
2025-08-30 13:25:01 - visual_tool - INFO - middleware - dispatch:112 - [TraceID: 1756531501833_312e1170] 请求开始: POST /visual_tools/v1/visual_tool
2025-08-30 13:25:01 - visual_tool - INFO - processor - apply_video_tool:47 - [TraceID: 1756531501833_312e1170] video_download_start
2025-08-30 13:25:01 - visual_tool - INFO - download_util - download_to_tmp_path:48 - [TraceID: 1756531501833_312e1170] download_start: url=https://mcp-env.bj.bcebos.com/v1/tmp/ForBiggerJoyrides.mp4?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T04%3A43%3A42Z%2F-1%2Fhost%2F0f079e07d12fc923c0ad3038821969c4dd3f6ee8e8c580bb956b7858db2b629a, path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-08-30/_tmp_download_krrckmf1.mp4, suffix=.mp4
2025-08-30 13:25:02 - visual_tool - INFO - download_util - download_to_tmp_path:60 - [TraceID: 1756531501833_312e1170] download_success: size_kb=2317.21, attempts=1, elapsed_sec=0.535
2025-08-30 13:25:02 - visual_tool - INFO - processor - apply_video_tool:77 - [TraceID: 1756531501833_312e1170] video duration: 15.05
2025-08-30 13:25:02 - visual_tool - INFO - processor - apply_video_tool:140 - [TraceID: 1756531501833_312e1170] tmp_cleanup_ok
2025-08-30 13:25:02 - visual_tool - INFO - trace_util - log_request_response:151 - [TraceID: 1756531501833_312e1170] 请求数据: {'name': 'video_clip_tool', 'version': '', 'call_id': '', 'arguments': {'start_time': 1, 'end_time': 15.5}, 'extra_params': {'video_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/ForBiggerJoyrides.mp4?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T04%3A43%3A42Z%2F-1%2Fhost%2F0f079e07d12fc923c0ad3038821969c4dd3f6ee8e8c580bb956b7858db2b629a'}}
2025-08-30 13:25:02 - visual_tool - ERROR - trace_util - log_request_response:155 - [TraceID: 1756531501833_312e1170] 请求处理失败: end_time (15.50) should be smaller or equal to the clip's duration (15.05).
2025-08-30 13:25:02 - visual_tool - INFO - middleware - dispatch:132 - [TraceID: 1756531501833_312e1170] 请求完成: 200 - 耗时: 763ms
2025-08-30 20:06:43 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-08-30 20:06:43 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-08-30 20:06:43 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-08-30 20:06:43 - uvicorn.error - INFO - server - _serve:94 - Finished server process [99507]
2025-09-03 12:57:21 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-09-03 12:57:21 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-09-03 12:57:21 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-09-03 12:57:21 - uvicorn.error - INFO - server - _serve:84 - Started server process [4566]
2025-09-03 12:57:21 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-09-03 12:57:21 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-09-03 12:57:21 - uvicorn.error - INFO - server - _log_started_message:216 - Uvicorn running on http://0.0.0.0:8080 (Press CTRL+C to quit)
2025-09-03 12:57:23 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-09-03 12:57:23 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-09-03 12:57:23 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-09-03 12:57:23 - uvicorn.error - INFO - server - _serve:94 - Finished server process [4566]
2025-09-03 18:30:56 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-09-03 18:30:56 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-09-03 18:30:56 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-09-03 18:30:56 - uvicorn.error - INFO - server - _serve:84 - Started server process [71017]
2025-09-03 18:30:56 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-09-03 18:30:56 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-09-03 18:30:56 - uvicorn.error - INFO - server - _log_started_message:216 - Uvicorn running on http://0.0.0.0:8080 (Press CTRL+C to quit)
2025-09-03 18:31:05 - visual_tool - INFO - middleware - dispatch:112 - [TraceID: 1756895465114_a7081dfa] 请求开始: POST /visual_tools/v1/visual_tool
2025-09-03 18:31:05 - visual_tool - INFO - middleware - dispatch:132 - [TraceID: 1756895465114_a7081dfa] 请求完成: 422 - 耗时: 2ms
2025-09-03 18:32:23 - visual_tool - INFO - middleware - dispatch:112 - [TraceID: 1756895543134_c2350c4e] 请求开始: POST /visual_tools/v1/visual_tool
2025-09-03 18:32:23 - visual_tool - INFO - middleware - dispatch:132 - [TraceID: 1756895543134_c2350c4e] 请求完成: 422 - 耗时: 4ms
2025-09-03 18:33:24 - visual_tool - INFO - middleware - dispatch:112 - [TraceID: 1756895604447_5915b233] 请求开始: POST /visual_tools/v1/visual_tool
2025-09-03 18:33:24 - visual_tool - INFO - middleware - dispatch:132 - [TraceID: 1756895604447_5915b233] 请求完成: 422 - 耗时: 3ms
2025-09-03 18:33:26 - visual_tool - INFO - middleware - dispatch:112 - [TraceID: 1756895606676_f158790d] 请求开始: POST /visual_tools/v1/visual_tool
2025-09-03 18:33:26 - visual_tool - INFO - middleware - dispatch:132 - [TraceID: 1756895606676_f158790d] 请求完成: 422 - 耗时: 6ms
2025-09-03 18:34:02 - visual_tool - INFO - middleware - dispatch:112 - [TraceID: 1756895642339_44ee6624] 请求开始: POST /visual_tools/v1/visual_tool
2025-09-03 18:34:02 - visual_tool - INFO - middleware - dispatch:132 - [TraceID: 1756895642339_44ee6624] 请求完成: 422 - 耗时: 13ms
2025-09-03 18:34:08 - visual_tool - INFO - middleware - dispatch:112 - [TraceID: 1756895648382_d9709923] 请求开始: POST /visual_tools/v1/visual_tool
2025-09-03 18:34:08 - visual_tool - INFO - processor - apply_video_tool:54 - [TraceID: 1756895648382_d9709923] video_download_start
2025-09-03 18:34:08 - visual_tool - INFO - download_util - download_to_tmp_path:48 - [TraceID: 1756895648382_d9709923] download_start: url=https://mcp-env.bj.bcebos.com/v1/tmp/ForBiggerJoyrides.mp4?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T23%3A01%3A23Z%2F-1%2Fhost%2Fea2304a9e21a4375955d9f4541ca8881343d4ef9f055211f99ae10b9e5d3aa4c, path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-09-03/_tmp_download_69fjscbx.mp4, suffix=.mp4
2025-09-03 18:34:08 - visual_tool - INFO - download_util - download_to_tmp_path:60 - [TraceID: 1756895648382_d9709923] download_success: size_kb=2317.21, attempts=1, elapsed_sec=0.441
2025-09-03 18:34:08 - visual_tool - INFO - processor - apply_video_tool:145 - [TraceID: 1756895648382_d9709923] tmp_cleanup_ok
2025-09-03 18:34:08 - visual_tool - INFO - trace_util - log_request_response:151 - [TraceID: 1756895648382_d9709923] 请求数据: {'name': 'video_clip_tool', 'version': '', 'call_id': '', 'arguments': {}, 'extra_params': {'video_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/ForBiggerJoyrides.mp4?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T23%3A01%3A23Z%2F-1%2Fhost%2Fea2304a9e21a4375955d9f4541ca8881343d4ef9f055211f99ae10b9e5d3aa4c'}}
2025-09-03 18:34:08 - visual_tool - ERROR - trace_util - log_request_response:155 - [TraceID: 1756895648382_d9709923] 请求处理失败: Missing required argument 'start_time' for video_clip_tool
2025-09-03 18:34:08 - visual_tool - INFO - middleware - dispatch:132 - [TraceID: 1756895648382_d9709923] 请求完成: 200 - 耗时: 461ms
2025-09-03 18:34:30 - visual_tool - INFO - middleware - dispatch:112 - [TraceID: 1756895670153_59585977] 请求开始: POST /visual_tools/v1/visual_tool
2025-09-03 18:34:30 - visual_tool - INFO - processor - apply_image_tool:88 - [TraceID: 1756895670153_59585977] image_download_start
2025-09-03 18:34:30 - visual_tool - INFO - download_util - download_to_tmp_path:48 - [TraceID: 1756895670153_59585977] download_start: url=https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073, path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-09-03/_tmp_download_s425t4r1.jpeg, suffix=.jpeg
2025-09-03 18:34:30 - visual_tool - INFO - download_util - download_to_tmp_path:60 - [TraceID: 1756895670153_59585977] download_success: size_kb=31.93, attempts=1, elapsed_sec=0.144
2025-09-03 18:34:30 - visual_tool - INFO - processor - apply_image_tool:92 - [TraceID: 1756895670153_59585977] image_download_success
2025-09-03 18:34:30 - visual_tool - INFO - processor - apply_image_tool:113 - [TraceID: 1756895670153_59585977] image_processing_start: tool=image_calchist_tool, width= 554, height= 554
2025-09-03 18:34:30 - visual_tool - INFO - processor - apply_image_tool:254 - [TraceID: 1756895670153_59585977] upload_invoke
2025-09-03 18:34:30 - visual_tool - INFO - upload_util - upload_file_to_bos:45 - [TraceID: 1756895670153_59585977] upload_start: file_path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-09-03/_tmpe9nc0vo_.jpeg, object_key=visual_tools/output/9e801c9852967c881d46e0290d348b17_1756895670601_82638.jpeg, size_kb=19.37
2025-09-03 18:34:30 - visual_tool - INFO - bos_util - get_bos_client:51 - Initializing BOS client...
2025-09-03 18:34:30 - visual_tool - INFO - bos_util - get_bos_client:62 - BOS client initialized successfully
2025-09-03 18:34:30 - visual_tool - INFO - upload_util - upload_file_to_bos:57 - [TraceID: 1756895670153_59585977] upload_success
2025-09-03 18:34:30 - visual_tool - INFO - processor - apply_image_tool:258 - [TraceID: 1756895670153_59585977] image_processing_done
2025-09-03 18:34:30 - visual_tool - INFO - trace_util - log_request_response:151 - [TraceID: 1756895670153_59585977] 请求数据: {'name': 'image_calchist_tool', 'version': '', 'call_id': '', 'arguments': {'channels': [0]}, 'extra_params': {'image_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073', 'image_data': '', 'return_data': False}}
2025-09-03 18:34:30 - visual_tool - INFO - trace_util - log_request_response:158 - [TraceID: 1756895670153_59585977] 响应数据: {'code': 0, 'message': 'Finish', 'data': '{"type": "image_url", "image_url": "https://mcp-env.bj.bcebos.com/visual_tools/output/9e801c9852967c881d46e0290d348b17_1756895670601_82638.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-09-03T10%3A34%3A30Z%2F-1%2F%2F29ac607b86f6a69a4b709624e1819f025411342cad67357398041213b5127df7"}'}
2025-09-03 18:34:30 - visual_tool - INFO - middleware - dispatch:132 - [TraceID: 1756895670153_59585977] 请求完成: 200 - 耗时: 548ms
2025-09-03 18:34:34 - visual_tool - INFO - middleware - dispatch:112 - [TraceID: 1756895674464_8609ff31] 请求开始: POST /visual_tools/v1/visual_tool
2025-09-03 18:34:34 - visual_tool - INFO - middleware - dispatch:132 - [TraceID: 1756895674464_8609ff31] 请求完成: 422 - 耗时: 4ms
2025-09-03 18:34:35 - visual_tool - INFO - middleware - dispatch:112 - [TraceID: 1756895675876_acce53b2] 请求开始: POST /visual_tools/v1/visual_tool
2025-09-03 18:34:35 - visual_tool - INFO - middleware - dispatch:132 - [TraceID: 1756895675876_acce53b2] 请求完成: 422 - 耗时: 1ms
2025-09-03 18:35:14 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-09-03 18:35:14 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-09-03 18:35:14 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-09-03 18:35:14 - uvicorn.error - INFO - server - _serve:94 - Finished server process [71017]
2025-09-03 18:35:33 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-09-03 18:35:33 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-09-03 18:35:33 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-09-03 18:35:33 - uvicorn.error - INFO - server - _serve:84 - Started server process [72508]
2025-09-03 18:35:33 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-09-03 18:35:33 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-09-03 18:35:54 - visual_tool - INFO - middleware - dispatch:112 - [TraceID: 1756895754913_39b57ed5] 请求开始: POST /visual_tools/v1/visual_tool
2025-09-03 18:35:54 - visual_tool - INFO - middleware - dispatch:132 - [TraceID: 1756895754913_39b57ed5] 请求完成: 422 - 耗时: 16ms
2025-09-03 18:36:01 - visual_tool - INFO - middleware - dispatch:112 - [TraceID: 1756895761389_22d881d0] 请求开始: POST /visual_tools/v1/visual_tool
2025-09-03 18:36:01 - visual_tool - INFO - middleware - dispatch:132 - [TraceID: 1756895761389_22d881d0] 请求完成: 422 - 耗时: 3ms
2025-09-03 18:36:06 - visual_tool - INFO - middleware - dispatch:112 - [TraceID: 1756895766356_20f442aa] 请求开始: POST /visual_tools/v1/visual_tool
2025-09-03 18:36:06 - visual_tool - INFO - middleware - dispatch:132 - [TraceID: 1756895766356_20f442aa] 请求完成: 422 - 耗时: 11ms
2025-09-03 18:36:12 - visual_tool - INFO - middleware - dispatch:112 - [TraceID: 1756895772747_87c07b50] 请求开始: POST /visual_tools/v1/visual_tool
2025-09-03 18:36:12 - visual_tool - INFO - middleware - dispatch:132 - [TraceID: 1756895772747_87c07b50] 请求完成: 422 - 耗时: 4ms
2025-09-03 18:36:20 - visual_tool - INFO - middleware - dispatch:112 - [TraceID: 1756895780484_72cde350] 请求开始: POST /visual_tools/v1/visual_tool
2025-09-03 18:36:20 - visual_tool - INFO - middleware - dispatch:132 - [TraceID: 1756895780484_72cde350] 请求完成: 422 - 耗时: 8ms
2025-09-03 18:36:29 - visual_tool - INFO - middleware - dispatch:112 - [TraceID: 1756895789521_40909a7b] 请求开始: POST /visual_tools/v1/visual_tool
2025-09-03 18:36:32 - visual_tool - INFO - processor - apply_image_tool:88 - [TraceID: 1756895789521_40909a7b] image_download_start
2025-09-03 18:36:32 - visual_tool - INFO - download_util - download_to_tmp_path:48 - [TraceID: 1756895789521_40909a7b] download_start: url=https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073, path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-09-03/_tmp_download_wfqmpmrv.jpeg, suffix=.jpeg
2025-09-03 18:36:33 - visual_tool - INFO - download_util - download_to_tmp_path:60 - [TraceID: 1756895789521_40909a7b] download_success: size_kb=31.93, attempts=1, elapsed_sec=0.163
2025-09-03 18:36:33 - visual_tool - INFO - processor - apply_image_tool:92 - [TraceID: 1756895789521_40909a7b] image_download_success
2025-09-03 18:36:33 - visual_tool - INFO - processor - apply_image_tool:113 - [TraceID: 1756895789521_40909a7b] image_processing_start: tool=image_calchist_tool, width= 554, height= 554
2025-09-03 18:36:33 - visual_tool - INFO - processor - apply_image_tool:254 - [TraceID: 1756895789521_40909a7b] upload_invoke
2025-09-03 18:36:33 - visual_tool - INFO - upload_util - upload_file_to_bos:45 - [TraceID: 1756895789521_40909a7b] upload_start: file_path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-09-03/_tmpykabkkm5.jpeg, object_key=visual_tools/output/9e801c9852967c881d46e0290d348b17_1756895793327_11400.jpeg, size_kb=19.37
2025-09-03 18:36:33 - visual_tool - INFO - bos_util - get_bos_client:51 - Initializing BOS client...
2025-09-03 18:36:33 - visual_tool - INFO - bos_util - get_bos_client:62 - BOS client initialized successfully
2025-09-03 18:36:33 - visual_tool - INFO - upload_util - upload_file_to_bos:57 - [TraceID: 1756895789521_40909a7b] upload_success
2025-09-03 18:36:33 - visual_tool - INFO - processor - apply_image_tool:258 - [TraceID: 1756895789521_40909a7b] image_processing_done
2025-09-03 18:36:33 - visual_tool - INFO - trace_util - log_request_response:151 - [TraceID: 1756895789521_40909a7b] 请求数据: {'name': 'image_calchist_tool', 'version': '', 'call_id': '', 'arguments': {'channels': [0]}, 'extra_params': {'image_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073', 'image_data': '', 'return_data': False}}
2025-09-03 18:36:33 - visual_tool - INFO - trace_util - log_request_response:158 - [TraceID: 1756895789521_40909a7b] 响应数据: {'code': 0, 'message': 'Finish', 'data': '{"type": "image_url", "image_url": "https://mcp-env.bj.bcebos.com/visual_tools/output/9e801c9852967c881d46e0290d348b17_1756895793327_11400.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-09-03T10%3A36%3A33Z%2F-1%2F%2F19c264a2484514bf1ac2ff9f402b8ac578f0173bbd1618e61e84dd627c306be7"}'}
2025-09-03 18:36:33 - visual_tool - INFO - middleware - dispatch:132 - [TraceID: 1756895789521_40909a7b] 请求完成: 200 - 耗时: 3.9s
2025-09-03 18:36:36 - visual_tool - INFO - middleware - dispatch:112 - [TraceID: 1756895796871_54bd1c48] 请求开始: POST /visual_tools/v1/visual_tool
2025-09-03 18:36:36 - visual_tool - INFO - middleware - dispatch:132 - [TraceID: 1756895796871_54bd1c48] 请求完成: 422 - 耗时: 2ms
2025-09-03 18:36:40 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-09-03 18:36:40 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-09-03 18:36:40 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-09-03 18:36:40 - uvicorn.error - INFO - server - _serve:94 - Finished server process [72508]
2025-09-03 18:38:04 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-09-03 18:38:04 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-09-03 18:38:04 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-09-03 18:38:04 - uvicorn.error - INFO - server - _serve:84 - Started server process [73732]
2025-09-03 18:38:04 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-09-03 18:38:04 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-09-03 18:38:06 - visual_tool - INFO - middleware - dispatch:112 - [TraceID: 1756895886127_2f078143] 请求开始: POST /visual_tools/v1/visual_tool
2025-09-03 18:38:06 - visual_tool - INFO - middleware - dispatch:132 - [TraceID: 1756895886127_2f078143] 请求完成: 422 - 耗时: 6ms
2025-09-03 18:38:25 - visual_tool - INFO - middleware - dispatch:112 - [TraceID: 1756895905173_9faf23ab] 请求开始: POST /visual_tools/v1/visual_tool
2025-09-03 18:38:25 - visual_tool - INFO - middleware - dispatch:132 - [TraceID: 1756895905173_9faf23ab] 请求完成: 422 - 耗时: 3ms
2025-09-03 18:38:48 - visual_tool - INFO - middleware - dispatch:112 - [TraceID: 1756895928108_6caf8a49] 请求开始: POST /visual_tools/v1/visual_tool
2025-09-03 18:38:48 - visual_tool - INFO - middleware - dispatch:132 - [TraceID: 1756895928108_6caf8a49] 请求完成: 422 - 耗时: 5ms
2025-09-03 18:38:48 - visual_tool - INFO - middleware - dispatch:112 - [TraceID: 1756895928843_6d777782] 请求开始: POST /visual_tools/v1/visual_tool
2025-09-03 18:38:48 - visual_tool - INFO - middleware - dispatch:132 - [TraceID: 1756895928843_6d777782] 请求完成: 422 - 耗时: 2ms
2025-09-03 18:39:24 - visual_tool - INFO - middleware - dispatch:112 - [TraceID: 1756895964443_d9d38681] 请求开始: POST /visual_tools/v1/visual_tool
2025-09-03 18:39:24 - visual_tool - INFO - middleware - dispatch:132 - [TraceID: 1756895964443_d9d38681] 请求完成: 422 - 耗时: 4ms
2025-09-03 18:39:24 - visual_tool - INFO - middleware - dispatch:112 - [TraceID: 1756895964978_a87c2f66] 请求开始: POST /visual_tools/v1/visual_tool
2025-09-03 18:39:24 - visual_tool - INFO - middleware - dispatch:132 - [TraceID: 1756895964978_a87c2f66] 请求完成: 422 - 耗时: 7ms
2025-09-03 18:41:30 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-09-03 18:41:30 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-09-03 18:41:30 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-09-03 18:41:30 - uvicorn.error - INFO - server - _serve:94 - Finished server process [73732]
2025-09-03 18:41:33 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-09-03 18:41:33 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-09-03 18:41:33 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-09-03 18:41:38 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-09-03 18:41:38 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-09-03 18:41:38 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-09-03 18:41:38 - uvicorn.error - INFO - server - _serve:84 - Started server process [74579]
2025-09-03 18:41:38 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-09-03 18:41:38 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-09-03 18:41:59 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-09-03 18:41:59 - uvicorn.error - INFO - server - _serve:94 - Finished server process [74579]
2025-09-03 18:42:05 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-09-03 18:42:05 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-09-03 18:42:05 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-09-03 18:42:05 - uvicorn.error - INFO - server - _serve:84 - Started server process [75263]
2025-09-03 18:42:05 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-09-03 18:42:05 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-09-03 18:42:09 - visual_tool - INFO - middleware - dispatch:112 - [TraceID: 1756896129819_c343b7b1] 请求开始: POST /visual_tools/v1/visual_tool
2025-09-03 18:42:09 - visual_tool - INFO - middleware - dispatch:132 - [TraceID: 1756896129819_c343b7b1] 请求完成: 422 - 耗时: 10ms
2025-09-03 18:42:15 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-09-03 18:42:16 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-09-03 18:42:16 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-09-03 18:42:16 - uvicorn.error - INFO - server - _serve:94 - Finished server process [75263]
2025-09-03 18:42:27 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-09-03 18:42:27 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-09-03 18:42:27 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-09-03 18:42:27 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-09-03 18:42:27 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-09-03 18:42:27 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-09-03 18:43:39 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-09-03 18:43:39 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-09-03 18:43:39 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-09-03 18:43:39 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-09-03 18:43:39 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-09-03 18:43:39 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-09-03 18:44:19 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-09-03 18:44:19 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-09-03 18:44:19 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-09-03 18:44:19 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-09-03 18:44:19 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-09-03 18:44:19 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-09-03 18:46:25 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-09-03 18:46:25 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-09-03 18:46:25 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-09-03 18:46:26 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-09-03 18:46:26 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-09-03 18:46:26 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-09-03 18:49:53 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-09-03 18:49:53 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-09-03 18:49:53 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-09-03 18:52:05 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-09-03 18:52:05 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-09-03 18:52:05 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-09-03 18:52:56 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-09-03 18:52:56 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-09-03 18:52:56 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-09-03 18:53:03 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-09-03 18:53:03 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-09-03 18:53:03 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-09-03 18:53:25 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-09-03 18:53:25 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-09-03 18:53:25 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-09-03 18:53:26 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-09-03 18:53:26 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-09-03 18:53:26 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-09-03 18:53:26 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-09-03 18:53:26 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-09-03 18:53:26 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-09-03 18:54:21 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-09-03 18:54:21 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-09-03 18:54:21 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-09-03 18:54:22 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-09-03 18:54:22 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-09-03 18:54:22 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-09-03 18:54:22 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-09-03 18:54:22 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-09-03 18:54:22 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-09-03 18:54:48 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-09-03 18:54:48 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-09-03 18:54:48 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-09-03 18:54:48 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-09-03 18:54:48 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-09-03 18:54:48 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-09-03 18:54:48 - uvicorn.error - INFO - server - _serve:84 - Started server process [83273]
2025-09-03 18:54:48 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-09-03 18:54:48 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-09-03 18:54:56 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-09-03 18:54:56 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-09-03 18:54:56 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-09-03 18:54:56 - uvicorn.error - INFO - server - _serve:94 - Finished server process [83273]
2025-09-03 18:54:57 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-09-03 18:54:57 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-09-03 18:54:57 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-09-03 18:54:57 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-09-03 18:54:57 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-09-03 18:54:57 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-09-03 18:54:57 - uvicorn.error - INFO - server - _serve:84 - Started server process [83314]
2025-09-03 18:54:57 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-09-03 18:54:57 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-09-03 18:55:09 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-09-03 18:55:09 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-09-03 18:55:09 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-09-03 18:55:09 - uvicorn.error - INFO - server - _serve:94 - Finished server process [83314]
2025-09-03 18:55:10 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-09-03 18:55:10 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-09-03 18:55:10 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-09-03 18:55:28 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-09-03 18:55:28 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-09-03 18:55:28 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-09-03 18:55:38 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-09-03 18:55:38 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-09-03 18:55:38 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-09-03 18:55:39 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-09-03 18:55:39 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-09-03 18:55:39 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-09-03 18:55:55 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-09-03 18:55:55 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-09-03 18:55:55 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-09-03 18:56:09 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-09-03 18:56:09 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-09-03 18:56:09 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-09-03 18:56:56 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-09-03 18:56:56 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-09-03 18:56:56 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-09-03 18:57:02 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-09-03 18:57:02 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-09-03 18:57:02 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-09-03 18:57:22 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-09-03 18:57:22 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-09-03 18:57:22 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-09-03 18:57:22 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-09-03 18:57:22 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-09-03 18:57:22 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-09-03 18:57:22 - uvicorn.error - INFO - server - _serve:84 - Started server process [84437]
2025-09-03 18:57:22 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-09-03 18:57:22 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-09-03 18:57:22 - uvicorn.error - INFO - server - _log_started_message:216 - Uvicorn running on http://0.0.0.0:8003 (Press CTRL+C to quit)
2025-09-03 18:57:24 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-09-03 18:57:24 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-09-03 18:57:24 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-09-03 18:57:24 - uvicorn.error - INFO - server - _serve:94 - Finished server process [84437]
2025-09-03 18:58:21 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-09-03 18:58:21 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-09-03 18:58:21 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-09-03 18:58:21 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-09-03 18:58:21 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-09-03 18:58:21 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-09-03 18:58:21 - uvicorn.error - INFO - server - _serve:84 - Started server process [84823]
2025-09-03 18:58:21 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-09-03 18:58:21 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-09-03 18:58:21 - uvicorn.error - INFO - server - _log_started_message:216 - Uvicorn running on http://0.0.0.0:8003 (Press CTRL+C to quit)
2025-09-03 18:58:53 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-09-03 18:58:53 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-09-03 18:58:53 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-09-03 18:58:53 - uvicorn.error - INFO - server - _serve:94 - Finished server process [84823]
2025-09-03 19:03:41 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-09-03 19:03:41 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-09-03 19:03:41 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-09-03 19:03:41 - uvicorn.error - INFO - server - _serve:84 - Started server process [87001]
2025-09-03 19:03:41 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-09-03 19:03:41 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-09-03 19:03:46 - visual_tool - INFO - middleware - dispatch:112 - [TraceID: 1756897426264_747d3a90] 请求开始: POST /visual_tools/v1/visual_tool
2025-09-03 19:03:46 - visual_tool - INFO - middleware - dispatch:132 - [TraceID: 1756897426264_747d3a90] 请求完成: 422 - 耗时: 4ms
2025-09-03 19:04:14 - visual_tool - INFO - middleware - dispatch:112 - [TraceID: 1756897454568_8f255051] 请求开始: POST /visual_tools/v1/visual_tool
2025-09-03 19:04:14 - visual_tool - INFO - middleware - dispatch:132 - [TraceID: 1756897454568_8f255051] 请求完成: 422 - 耗时: 6ms
2025-09-03 19:04:28 - visual_tool - INFO - middleware - dispatch:112 - [TraceID: 1756897468766_410e6ed2] 请求开始: POST /visual_tools/v1/visual_tool
2025-09-03 19:04:28 - visual_tool - INFO - app - api_v1:90 - [TraceID: 1756897468766_410e6ed2] Raw request body: {
    "name": "image_zoom_in_tool",
    "arguments": {
        "norm":false,
        "bbox_2d": [
            0,0,256,256
        ]
    },
    "extra_params": {

        "return_data": false
    }
}
2025-09-03 19:04:28 - visual_tool - INFO - app - api_v1:91 - [TraceID: 1756897468766_410e6ed2] Request body length: 198
2025-09-03 19:04:50 - visual_tool - INFO - app - api_v1:92 - [TraceID: 1756897468766_410e6ed2] Character at position 104: ' '
2025-09-03 19:04:50 - visual_tool - INFO - trace_util - log_request_response:151 - [TraceID: 1756897468766_410e6ed2] 请求数据: {'name': 'image_zoom_in_tool', 'version': '', 'call_id': '', 'arguments': {'norm': False, 'bbox_2d': [0, 0, 256, 256]}, 'extra_params': {'return_data': False}}
2025-09-03 19:04:50 - visual_tool - ERROR - trace_util - log_request_response:155 - [TraceID: 1756897468766_410e6ed2] 请求处理失败: either extra_params.image_url or extra_params.image_data is required for image tools
2025-09-03 19:04:50 - visual_tool - INFO - middleware - dispatch:132 - [TraceID: 1756897468766_410e6ed2] 请求完成: 200 - 耗时: 21.6s
2025-09-03 19:05:12 - visual_tool - INFO - middleware - dispatch:112 - [TraceID: 1756897512213_57416134] 请求开始: POST /visual_tools/v1/visual_tool
2025-09-03 19:05:12 - visual_tool - INFO - app - api_v1:90 - [TraceID: 1756897512213_57416134] Raw request body: {
    "name": "video_clip_tool",
    "arguments": {
        },
    "extra_params": {
        "video_url": "https://mcp-env.bj.bcebos.com/v1/tmp/ForBiggerJoyrides.mp4?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T23%3A01%3A23Z%2F-1%2Fhost%2Fea2304a9e21a4375955d9f4541ca8881343d4ef9f055211f99ae10b9e5d3aa4c"
    }
}
2025-09-03 19:05:12 - visual_tool - INFO - app - api_v1:91 - [TraceID: 1756897512213_57416134] Request body length: 335
2025-09-03 19:05:12 - visual_tool - INFO - app - api_v1:92 - [TraceID: 1756897512213_57416134] Character at position 104: '"'
2025-09-03 19:05:12 - visual_tool - INFO - processor - apply_video_tool:54 - [TraceID: 1756897512213_57416134] video_download_start
2025-09-03 19:05:12 - visual_tool - INFO - download_util - download_to_tmp_path:48 - [TraceID: 1756897512213_57416134] download_start: url=https://mcp-env.bj.bcebos.com/v1/tmp/ForBiggerJoyrides.mp4?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T23%3A01%3A23Z%2F-1%2Fhost%2Fea2304a9e21a4375955d9f4541ca8881343d4ef9f055211f99ae10b9e5d3aa4c, path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-09-03/_tmp_download_qgdvzpt1.mp4, suffix=.mp4
2025-09-03 19:05:12 - visual_tool - INFO - download_util - download_to_tmp_path:60 - [TraceID: 1756897512213_57416134] download_success: size_kb=2317.21, attempts=1, elapsed_sec=0.76
2025-09-03 19:05:12 - visual_tool - INFO - processor - apply_video_tool:145 - [TraceID: 1756897512213_57416134] tmp_cleanup_ok
2025-09-03 19:05:12 - visual_tool - INFO - trace_util - log_request_response:151 - [TraceID: 1756897512213_57416134] 请求数据: {'name': 'video_clip_tool', 'version': '', 'call_id': '', 'arguments': {}, 'extra_params': {'video_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/ForBiggerJoyrides.mp4?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T23%3A01%3A23Z%2F-1%2Fhost%2Fea2304a9e21a4375955d9f4541ca8881343d4ef9f055211f99ae10b9e5d3aa4c'}}
2025-09-03 19:05:12 - visual_tool - ERROR - trace_util - log_request_response:155 - [TraceID: 1756897512213_57416134] 请求处理失败: Missing required argument 'start_time' for video_clip_tool
2025-09-03 19:05:12 - visual_tool - INFO - middleware - dispatch:132 - [TraceID: 1756897512213_57416134] 请求完成: 200 - 耗时: 776ms
2025-09-03 19:05:24 - visual_tool - INFO - middleware - dispatch:112 - [TraceID: 1756897524733_0a4c1e68] 请求开始: POST /visual_tools/v1/visual_tool
2025-09-03 19:05:24 - visual_tool - INFO - app - api_v1:90 - [TraceID: 1756897524733_0a4c1e68] Raw request body: {
    "name": "video_clip_tool",
    "arguments": {
        "start_time": 1
    },
    "extra_params": {
        "video_url": "https://mcp-env.bj.bcebos.com/v1/tmp/ForBiggerJoyrides.mp4?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T23%3A01%3A23Z%2F-1%2Fhost%2Fea2304a9e21a4375955d9f4541ca8881343d4ef9f055211f99ae10b9e5d3aa4c"
    }
}
2025-09-03 19:05:24 - visual_tool - INFO - app - api_v1:91 - [TraceID: 1756897524733_0a4c1e68] Request body length: 355
2025-09-03 19:05:24 - visual_tool - INFO - app - api_v1:92 - [TraceID: 1756897524733_0a4c1e68] Character at position 104: '{'
2025-09-03 19:05:24 - visual_tool - INFO - processor - apply_video_tool:54 - [TraceID: 1756897524733_0a4c1e68] video_download_start
2025-09-03 19:05:24 - visual_tool - INFO - download_util - download_to_tmp_path:48 - [TraceID: 1756897524733_0a4c1e68] download_start: url=https://mcp-env.bj.bcebos.com/v1/tmp/ForBiggerJoyrides.mp4?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T23%3A01%3A23Z%2F-1%2Fhost%2Fea2304a9e21a4375955d9f4541ca8881343d4ef9f055211f99ae10b9e5d3aa4c, path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-09-03/_tmp_download_tsmqb277.mp4, suffix=.mp4
2025-09-03 19:05:25 - visual_tool - INFO - download_util - download_to_tmp_path:60 - [TraceID: 1756897524733_0a4c1e68] download_success: size_kb=2317.21, attempts=1, elapsed_sec=0.458
2025-09-03 19:05:25 - visual_tool - INFO - processor - apply_video_tool:145 - [TraceID: 1756897524733_0a4c1e68] tmp_cleanup_ok
2025-09-03 19:05:25 - visual_tool - INFO - trace_util - log_request_response:151 - [TraceID: 1756897524733_0a4c1e68] 请求数据: {'name': 'video_clip_tool', 'version': '', 'call_id': '', 'arguments': {'start_time': 1}, 'extra_params': {'video_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/ForBiggerJoyrides.mp4?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T23%3A01%3A23Z%2F-1%2Fhost%2Fea2304a9e21a4375955d9f4541ca8881343d4ef9f055211f99ae10b9e5d3aa4c'}}
2025-09-03 19:05:25 - visual_tool - ERROR - trace_util - log_request_response:155 - [TraceID: 1756897524733_0a4c1e68] 请求处理失败: Missing required argument 'end_time' for video_clip_tool
2025-09-03 19:05:25 - visual_tool - INFO - middleware - dispatch:132 - [TraceID: 1756897524733_0a4c1e68] 请求完成: 200 - 耗时: 466ms
2025-09-03 19:05:31 - visual_tool - INFO - middleware - dispatch:112 - [TraceID: 1756897531148_e258c951] 请求开始: POST /visual_tools/v1/visual_tool
2025-09-03 19:05:31 - visual_tool - INFO - middleware - dispatch:132 - [TraceID: 1756897531148_e258c951] 请求完成: 422 - 耗时: 7ms
2025-09-03 19:05:34 - visual_tool - INFO - middleware - dispatch:112 - [TraceID: 1756897534098_4402e3c3] 请求开始: POST /visual_tools/v1/visual_tool
2025-09-03 19:05:34 - visual_tool - INFO - app - api_v1:90 - [TraceID: 1756897534098_4402e3c3] Raw request body: {
    "name": "video_clip_tool",
    "arguments": {
        "start_time": 1,
        "end_time": 2
    },
    "extra_params": {
        "video_url": "https://mcp-env.bj.bcebos.com/v1/tmp/ForBiggerJoyrides.mp4?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T23%3A01%3A23Z%2F-1%2Fhost%2Fea2304a9e21a4375955d9f4541ca8881343d4ef9f055211f99ae10b9e5d3aa4c"
    }
}
2025-09-03 19:05:34 - visual_tool - INFO - app - api_v1:91 - [TraceID: 1756897534098_4402e3c3] Request body length: 378
2025-09-03 19:05:34 - visual_tool - INFO - app - api_v1:92 - [TraceID: 1756897534098_4402e3c3] Character at position 104: '}'
2025-09-03 19:05:34 - visual_tool - INFO - processor - apply_video_tool:54 - [TraceID: 1756897534098_4402e3c3] video_download_start
2025-09-03 19:05:34 - visual_tool - INFO - download_util - download_to_tmp_path:48 - [TraceID: 1756897534098_4402e3c3] download_start: url=https://mcp-env.bj.bcebos.com/v1/tmp/ForBiggerJoyrides.mp4?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T23%3A01%3A23Z%2F-1%2Fhost%2Fea2304a9e21a4375955d9f4541ca8881343d4ef9f055211f99ae10b9e5d3aa4c, path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-09-03/_tmp_download_wigf2f7i.mp4, suffix=.mp4
2025-09-03 19:05:34 - visual_tool - INFO - download_util - download_to_tmp_path:60 - [TraceID: 1756897534098_4402e3c3] download_success: size_kb=2317.21, attempts=1, elapsed_sec=0.474
2025-09-03 19:05:34 - visual_tool - INFO - processor - apply_video_tool:82 - [TraceID: 1756897534098_4402e3c3] video duration: 15.05
2025-09-03 19:05:35 - visual_tool - INFO - processor - apply_video_tool:91 - [TraceID: 1756897534098_4402e3c3] upload_invoke
2025-09-03 19:05:35 - visual_tool - INFO - upload_util - upload_file_to_bos:45 - [TraceID: 1756897534098_4402e3c3] upload_start: file_path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-09-03/_tmp_out_hrueog69.mp4, object_key=visual_tools/output/f77818abd11777eba54ff11b6dae8160_1756897535218_23390.mp4, size_kb=153.52
2025-09-03 19:05:35 - visual_tool - INFO - bos_util - get_bos_client:51 - Initializing BOS client...
2025-09-03 19:05:35 - visual_tool - INFO - bos_util - get_bos_client:62 - BOS client initialized successfully
2025-09-03 19:05:35 - visual_tool - INFO - upload_util - upload_file_to_bos:57 - [TraceID: 1756897534098_4402e3c3] upload_success
2025-09-03 19:05:35 - visual_tool - INFO - processor - apply_video_tool:95 - [TraceID: 1756897534098_4402e3c3] video_processing_done
2025-09-03 19:05:35 - visual_tool - INFO - processor - apply_video_tool:145 - [TraceID: 1756897534098_4402e3c3] tmp_cleanup_ok
2025-09-03 19:05:35 - visual_tool - INFO - trace_util - log_request_response:151 - [TraceID: 1756897534098_4402e3c3] 请求数据: {'name': 'video_clip_tool', 'version': '', 'call_id': '', 'arguments': {'start_time': 1, 'end_time': 2}, 'extra_params': {'video_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/ForBiggerJoyrides.mp4?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T23%3A01%3A23Z%2F-1%2Fhost%2Fea2304a9e21a4375955d9f4541ca8881343d4ef9f055211f99ae10b9e5d3aa4c'}}
2025-09-03 19:05:35 - visual_tool - INFO - trace_util - log_request_response:158 - [TraceID: 1756897534098_4402e3c3] 响应数据: {'code': 0, 'message': 'Finish', 'data': '{"type": "video_url", "video_url": "https://mcp-env.bj.bcebos.com/visual_tools/output/f77818abd11777eba54ff11b6dae8160_1756897535218_23390.mp4?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-09-03T11%3A05%3A35Z%2F-1%2F%2Fb502d1cce17ebc0afdd337e217cf1c819f4c4b8372172e5ef206a1d2befac81e"}'}
2025-09-03 19:05:35 - visual_tool - INFO - middleware - dispatch:132 - [TraceID: 1756897534098_4402e3c3] 请求完成: 200 - 耗时: 1.4s
2025-09-03 19:05:41 - visual_tool - INFO - middleware - dispatch:112 - [TraceID: 1756897541675_9f9d3740] 请求开始: POST /visual_tools/v1/visual_tool
2025-09-03 19:05:41 - visual_tool - INFO - app - api_v1:90 - [TraceID: 1756897541675_9f9d3740] Raw request body: {
    "name": "video_clip_tool",
    "arguments": {
        "start_time": 1,
        "end_time": 0
    },
    "extra_params": {
        "video_url": "https://mcp-env.bj.bcebos.com/v1/tmp/ForBiggerJoyrides.mp4?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T23%3A01%3A23Z%2F-1%2Fhost%2Fea2304a9e21a4375955d9f4541ca8881343d4ef9f055211f99ae10b9e5d3aa4c"
    }
}
2025-09-03 19:05:41 - visual_tool - INFO - app - api_v1:91 - [TraceID: 1756897541675_9f9d3740] Request body length: 378
2025-09-03 19:05:41 - visual_tool - INFO - app - api_v1:92 - [TraceID: 1756897541675_9f9d3740] Character at position 104: '}'
2025-09-03 19:05:41 - visual_tool - INFO - processor - apply_video_tool:54 - [TraceID: 1756897541675_9f9d3740] video_download_start
2025-09-03 19:05:41 - visual_tool - INFO - download_util - download_to_tmp_path:48 - [TraceID: 1756897541675_9f9d3740] download_start: url=https://mcp-env.bj.bcebos.com/v1/tmp/ForBiggerJoyrides.mp4?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T23%3A01%3A23Z%2F-1%2Fhost%2Fea2304a9e21a4375955d9f4541ca8881343d4ef9f055211f99ae10b9e5d3aa4c, path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-09-03/_tmp_download_nvmxwup0.mp4, suffix=.mp4
2025-09-03 19:05:42 - visual_tool - INFO - download_util - download_to_tmp_path:60 - [TraceID: 1756897541675_9f9d3740] download_success: size_kb=2317.21, attempts=1, elapsed_sec=0.398
2025-09-03 19:05:42 - visual_tool - INFO - processor - apply_video_tool:145 - [TraceID: 1756897541675_9f9d3740] tmp_cleanup_ok
2025-09-03 19:05:42 - visual_tool - INFO - trace_util - log_request_response:151 - [TraceID: 1756897541675_9f9d3740] 请求数据: {'name': 'video_clip_tool', 'version': '', 'call_id': '', 'arguments': {'start_time': 1, 'end_time': 0}, 'extra_params': {'video_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/ForBiggerJoyrides.mp4?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T23%3A01%3A23Z%2F-1%2Fhost%2Fea2304a9e21a4375955d9f4541ca8881343d4ef9f055211f99ae10b9e5d3aa4c'}}
2025-09-03 19:05:42 - visual_tool - ERROR - trace_util - log_request_response:155 - [TraceID: 1756897541675_9f9d3740] 请求处理失败: start_time must be less than end_time
2025-09-03 19:05:42 - visual_tool - INFO - middleware - dispatch:132 - [TraceID: 1756897541675_9f9d3740] 请求完成: 200 - 耗时: 417ms
2025-09-03 19:05:44 - visual_tool - INFO - middleware - dispatch:112 - [TraceID: 1756897544642_e4371920] 请求开始: POST /visual_tools/v1/visual_tool
2025-09-03 19:05:44 - visual_tool - INFO - app - api_v1:90 - [TraceID: 1756897544642_e4371920] Raw request body: {
    "name": "video_clip_tool",
    "arguments": {
        "start_time": 1,
        "end_time": 0
    },
    "extra_params": {
        "video_url": "https://mcp-env.bj.bcebos.com/v1/tmp/ForBiggerJoyrides.mp4?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T23%3A01%3A23Z%2F-1%2Fhost%2Fea2304a9e21a4375955d9f4541ca8881343d4ef9f055211f99ae10b9e5d3aa4c"
    }
}
2025-09-03 19:05:44 - visual_tool - INFO - app - api_v1:91 - [TraceID: 1756897544642_e4371920] Request body length: 378
2025-09-03 19:05:44 - visual_tool - INFO - app - api_v1:92 - [TraceID: 1756897544642_e4371920] Character at position 104: '}'
2025-09-03 19:05:44 - visual_tool - INFO - processor - apply_video_tool:54 - [TraceID: 1756897544642_e4371920] video_download_start
2025-09-03 19:05:44 - visual_tool - INFO - download_util - download_to_tmp_path:48 - [TraceID: 1756897544642_e4371920] download_start: url=https://mcp-env.bj.bcebos.com/v1/tmp/ForBiggerJoyrides.mp4?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T23%3A01%3A23Z%2F-1%2Fhost%2Fea2304a9e21a4375955d9f4541ca8881343d4ef9f055211f99ae10b9e5d3aa4c, path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-09-03/_tmp_download_fdftkz82.mp4, suffix=.mp4
2025-09-03 19:05:45 - visual_tool - INFO - download_util - download_to_tmp_path:60 - [TraceID: 1756897544642_e4371920] download_success: size_kb=2317.21, attempts=1, elapsed_sec=0.485
2025-09-03 19:05:45 - visual_tool - INFO - processor - apply_video_tool:145 - [TraceID: 1756897544642_e4371920] tmp_cleanup_ok
2025-09-03 19:05:45 - visual_tool - INFO - trace_util - log_request_response:151 - [TraceID: 1756897544642_e4371920] 请求数据: {'name': 'video_clip_tool', 'version': '', 'call_id': '', 'arguments': {'start_time': 1, 'end_time': 0}, 'extra_params': {'video_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/ForBiggerJoyrides.mp4?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T23%3A01%3A23Z%2F-1%2Fhost%2Fea2304a9e21a4375955d9f4541ca8881343d4ef9f055211f99ae10b9e5d3aa4c'}}
2025-09-03 19:05:45 - visual_tool - ERROR - trace_util - log_request_response:155 - [TraceID: 1756897544642_e4371920] 请求处理失败: start_time must be less than end_time
2025-09-03 19:05:45 - visual_tool - INFO - middleware - dispatch:132 - [TraceID: 1756897544642_e4371920] 请求完成: 200 - 耗时: 515ms
2025-09-03 19:06:11 - visual_tool - INFO - middleware - dispatch:112 - [TraceID: 1756897571743_fdc9bb53] 请求开始: POST /visual_tools/v1/visual_tool
2025-09-03 19:06:11 - visual_tool - INFO - app - api_v1:90 - [TraceID: 1756897571743_fdc9bb53] Raw request body: {
    "name": "video_select_frames_tool",
    "arguments": {
        "target_frame": [
            1,
            2,
            3
        ]
    },
    "extra_params": {
        "video_url": "https://mcp-env.bj.bcebos.com/v1/tmp/ForBiggerJoyrides.mp4?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T23%3A01%3A23Z%2F-1%2Fhost%2Fea2304a9e21a4375955d9f4541ca8881343d4ef9f055211f99ae10b9e5d3aa4c"
    }
}
2025-09-03 19:06:11 - visual_tool - INFO - app - api_v1:91 - [TraceID: 1756897571743_fdc9bb53] Request body length: 420
2025-09-03 19:06:11 - visual_tool - INFO - app - api_v1:92 - [TraceID: 1756897571743_fdc9bb53] Character at position 104: ' '
2025-09-03 19:06:11 - visual_tool - INFO - processor - apply_video_tool:54 - [TraceID: 1756897571743_fdc9bb53] video_download_start
2025-09-03 19:06:11 - visual_tool - INFO - download_util - download_to_tmp_path:48 - [TraceID: 1756897571743_fdc9bb53] download_start: url=https://mcp-env.bj.bcebos.com/v1/tmp/ForBiggerJoyrides.mp4?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T23%3A01%3A23Z%2F-1%2Fhost%2Fea2304a9e21a4375955d9f4541ca8881343d4ef9f055211f99ae10b9e5d3aa4c, path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-09-03/_tmp_download_mbo5_4nv.mp4, suffix=.mp4
2025-09-03 19:06:12 - visual_tool - INFO - download_util - download_to_tmp_path:60 - [TraceID: 1756897571743_fdc9bb53] download_success: size_kb=2317.21, attempts=1, elapsed_sec=0.585
2025-09-03 19:06:12 - visual_tool - INFO - processor - apply_video_tool:112 - [TraceID: 1756897571743_fdc9bb53] video_processing_start
2025-09-03 19:06:12 - visual_tool - INFO - processor - apply_video_tool:121 - [TraceID: 1756897571743_fdc9bb53] upload_invoke: frame_idx=1, size_kb=674.61, frame_path=./tmp/2025-09-03/_tmp_frame_1.png
2025-09-03 19:06:12 - visual_tool - INFO - upload_util - upload_file_to_bos:45 - [TraceID: 1756897571743_fdc9bb53] upload_start: file_path=./tmp/2025-09-03/_tmp_frame_1.png, object_key=visual_tools/output/9f118c7bd7bed25edf90eafaa8256f09_1756897572440_54709.png, size_kb=674.61
2025-09-03 19:06:12 - visual_tool - INFO - upload_util - upload_file_to_bos:57 - [TraceID: 1756897571743_fdc9bb53] upload_success
2025-09-03 19:06:12 - visual_tool - INFO - processor - apply_video_tool:130 - [TraceID: 1756897571743_fdc9bb53] tmp_cleanup_ok
2025-09-03 19:06:12 - visual_tool - INFO - processor - apply_video_tool:121 - [TraceID: 1756897571743_fdc9bb53] upload_invoke: frame_idx=2, size_kb=698.12, frame_path=./tmp/2025-09-03/_tmp_frame_2.png
2025-09-03 19:06:12 - visual_tool - INFO - upload_util - upload_file_to_bos:45 - [TraceID: 1756897571743_fdc9bb53] upload_start: file_path=./tmp/2025-09-03/_tmp_frame_2.png, object_key=visual_tools/output/420fc41c61c4248e0a9af2fa311f7494_1756897572808_21761.png, size_kb=698.12
2025-09-03 19:06:13 - visual_tool - INFO - upload_util - upload_file_to_bos:57 - [TraceID: 1756897571743_fdc9bb53] upload_success
2025-09-03 19:06:13 - visual_tool - INFO - processor - apply_video_tool:130 - [TraceID: 1756897571743_fdc9bb53] tmp_cleanup_ok
2025-09-03 19:06:13 - visual_tool - INFO - processor - apply_video_tool:121 - [TraceID: 1756897571743_fdc9bb53] upload_invoke: frame_idx=3, size_kb=717.43, frame_path=./tmp/2025-09-03/_tmp_frame_3.png
2025-09-03 19:06:13 - visual_tool - INFO - upload_util - upload_file_to_bos:45 - [TraceID: 1756897571743_fdc9bb53] upload_start: file_path=./tmp/2025-09-03/_tmp_frame_3.png, object_key=visual_tools/output/b3dd6ef98014ea567795ddefb2af892c_1756897573173_84449.png, size_kb=717.43
2025-09-03 19:06:13 - visual_tool - INFO - upload_util - upload_file_to_bos:57 - [TraceID: 1756897571743_fdc9bb53] upload_success
2025-09-03 19:06:13 - visual_tool - INFO - processor - apply_video_tool:130 - [TraceID: 1756897571743_fdc9bb53] tmp_cleanup_ok
2025-09-03 19:06:13 - visual_tool - INFO - processor - apply_video_tool:137 - [TraceID: 1756897571743_fdc9bb53] video_processing_done
2025-09-03 19:06:13 - visual_tool - INFO - processor - apply_video_tool:145 - [TraceID: 1756897571743_fdc9bb53] tmp_cleanup_ok
2025-09-03 19:06:13 - visual_tool - INFO - trace_util - log_request_response:151 - [TraceID: 1756897571743_fdc9bb53] 请求数据: {'name': 'video_select_frames_tool', 'version': '', 'call_id': '', 'arguments': {'target_frame': [1, 2, 3]}, 'extra_params': {'video_url': 'https://mcp-env.bj.bcebos.com/v1/tmp/ForBiggerJoyrides.mp4?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-30T23%3A01%3A23Z%2F-1%2Fhost%2Fea2304a9e21a4375955d9f4541ca8881343d4ef9f055211f99ae10b9e5d3aa4c'}}
2025-09-03 19:06:13 - visual_tool - INFO - trace_util - log_request_response:158 - [TraceID: 1756897571743_fdc9bb53] 响应数据: {'code': 0, 'message': 'Finish', 'data': '{"type": "image_url_list", "image_url_list": ["https://mcp-env.bj.bcebos.com/visual_tools/output/9f118c7bd7bed25edf90eafaa8256f09_1756897572440_54709.png?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-09-03T11%3A06%3A12Z%2F-1%2F%2F3f33ebd94b12841297e3ee220174aa4e9b61a961d0fd8a9ab9aca5a33cb1b810", "https://mcp-env.bj.bcebos.com/visual_tools/output/420fc41c61c4248e0a9af2fa311f7494_1756897572808_21761.png?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-09-03T11%3A06%3A13Z%2F-1%2F%2Fd1d0bcb8434d9c309a0f7042aff90b9b2de0c5ff392454e3f0f8b2db5e735e40", "https://mcp-env.bj.bcebos.com/visual_tools/output/b3dd6ef98014ea567795ddefb2af892c_1756897573173_84449.png?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-09-03T11%3A06%3A13Z%2F-1%2F%2Fb8aa028fa9e5625da163b6525eb986b395288f6cf69c7fcda510616dde0ca612"]}'}
2025-09-03 19:06:13 - visual_tool - INFO - middleware - dispatch:132 - [TraceID: 1756897571743_fdc9bb53] 请求完成: 200 - 耗时: 2.1s
2025-09-03 19:07:23 - visual_tool - INFO - middleware - dispatch:112 - [TraceID: 1756897643786_94bc4b94] 请求开始: POST /visual_tools/v1/visual_tool
2025-09-03 19:07:23 - visual_tool - INFO - app - api_v1:90 - [TraceID: 1756897643786_94bc4b94] Raw request body: {
    "name": "image_calchist_tool",
    "extra_params": {
        "image_url": "https://paddlenlp.bj.bcebos.com/models/community/paddlemix/datasets/DeepEyes/imgs/000000336492.jpg"
    }
}
2025-09-03 19:07:23 - visual_tool - INFO - app - api_v1:91 - [TraceID: 1756897643786_94bc4b94] Request body length: 188
2025-09-03 19:07:23 - visual_tool - INFO - app - api_v1:92 - [TraceID: 1756897643786_94bc4b94] Character at position 104: 'c'
2025-09-03 19:07:23 - visual_tool - INFO - processor - apply_image_tool:88 - [TraceID: 1756897643786_94bc4b94] image_download_start
2025-09-03 19:07:23 - visual_tool - INFO - download_util - download_to_tmp_path:48 - [TraceID: 1756897643786_94bc4b94] download_start: url=https://paddlenlp.bj.bcebos.com/models/community/paddlemix/datasets/DeepEyes/imgs/000000336492.jpg, path=/Users/<USER>/code/baidu/dataeng/visual_tools/tmp/2025-09-03/_tmp_download_uxlrg0k5.jpg, suffix=.jpg
2025-09-03 19:07:24 - visual_tool - INFO - download_util - download_to_tmp_path:60 - [TraceID: 1756897643786_94bc4b94] download_success: size_kb=95.24, attempts=1, elapsed_sec=0.285
2025-09-03 19:07:24 - visual_tool - INFO - processor - apply_image_tool:92 - [TraceID: 1756897643786_94bc4b94] image_download_success
2025-09-03 19:07:24 - visual_tool - INFO - processor - apply_image_tool:113 - [TraceID: 1756897643786_94bc4b94] image_processing_start: tool=image_calchist_tool, width= 464, height= 640
2025-09-03 19:07:24 - visual_tool - INFO - trace_util - log_request_response:151 - [TraceID: 1756897643786_94bc4b94] 请求数据: {'name': 'image_calchist_tool', 'version': '', 'call_id': '', 'arguments': {}, 'extra_params': {'image_url': 'https://paddlenlp.bj.bcebos.com/models/community/paddlemix/datasets/DeepEyes/imgs/000000336492.jpg'}}
2025-09-03 19:07:24 - visual_tool - ERROR - trace_util - log_request_response:155 - [TraceID: 1756897643786_94bc4b94] 请求处理失败: Missing required argument 'channels' for image_calchist_tool
2025-09-03 19:07:24 - visual_tool - INFO - middleware - dispatch:132 - [TraceID: 1756897643786_94bc4b94] 请求完成: 200 - 耗时: 859ms
2025-09-03 19:07:36 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-09-03 19:07:36 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-09-03 19:07:36 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-09-03 19:07:36 - uvicorn.error - INFO - server - _serve:94 - Finished server process [87001]
2025-09-03 19:07:38 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-09-03 19:07:38 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-09-03 19:07:38 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-09-03 19:07:38 - uvicorn.error - INFO - server - _serve:84 - Started server process [87742]
2025-09-03 19:07:38 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-09-03 19:07:38 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
2025-09-03 19:08:06 - uvicorn.error - INFO - server - shutdown:264 - Shutting down
2025-09-03 19:08:06 - uvicorn.error - INFO - on - shutdown:67 - Waiting for application shutdown.
2025-09-03 19:08:06 - uvicorn.error - INFO - on - shutdown:76 - Application shutdown complete.
2025-09-03 19:08:06 - uvicorn.error - INFO - server - _serve:94 - Finished server process [87742]
2025-09-03 19:08:08 - visual_tool - INFO - logging_util - setup_logging:52 - 日志系统初始化完成
2025-09-03 19:08:08 - visual_tool - INFO - logging_util - setup_logging:53 - 日志配置文件: logging_config.yaml
2025-09-03 19:08:08 - visual_tool - INFO - logging_util - setup_logging:54 - 日志目录: logs
2025-09-03 19:08:08 - uvicorn.error - INFO - server - _serve:84 - Started server process [87833]
2025-09-03 19:08:08 - uvicorn.error - INFO - on - startup:48 - Waiting for application startup.
2025-09-03 19:08:08 - uvicorn.error - INFO - on - startup:62 - Application startup complete.
