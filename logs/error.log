2025-08-29 21:15:12 - visual_tool - ERROR - download_util - download_to_tmp_path:75 - [TraceID: 1756473312082_b3633e91] download_attempt_failed
2025-08-29 21:15:13 - visual_tool - ERROR - download_util - download_to_tmp_path:75 - [TraceID: 1756473312082_b3633e91] download_attempt_failed
2025-08-29 21:15:14 - visual_tool - ERROR - download_util - download_to_tmp_path:75 - [TraceID: 1756473312082_b3633e91] download_attempt_failed
2025-08-29 21:15:14 - visual_tool - ERROR - download_util - download_to_tmp_path:85 - [TraceID: 1756473312082_b3633e91] download_failed
2025-08-29 21:15:14 - visual_tool - ERROR - processor - apply_image_tool:80 - [TraceID: 1756473312082_b3633e91] image_download_failed
2025-08-29 21:15:14 - visual_tool - ERROR - trace_util - log_request_response:155 - [TraceID: 1756473312082_b3633e91] 请求处理失败: failed to download image_url: HTTPSConnectionPool(host='mcp-env.bj.bcebos.com', port=443): Max retries exceeded with url: /v1/tmp/iris.jpeg?authorization=bce-auth-v1%2FALTAKfgI1uR7BgxHKOrqHxauEN%2F2025-08-15T11%3A07%3A12Z%2F-1%2Fhost%2F3e0a9aa78685ec38dbd150134196c00a6643604bc9bfd654033176fedf631073 (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x110785f00>, 'Connection to mcp-env.bj.bcebos.com timed out. (connect timeout=0.5)'))
2025-08-30 09:55:24 - visual_tool - ERROR - download_util - download_to_tmp_path:61 - download_attempt_failed: url=https://invalid-url-that-does-not-exist.com/test.jpg, attempt=1, error=HTTPSConnectionPool(host='invalid-url-that-does-not-exist.com', port=443): Max retries exceeded with url: /test.jpg (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x101781700>, 'Connection to invalid-url-that-does-not-exist.com timed out. (connect timeout=0.5)'))
2025-08-30 09:56:21 - visual_tool - ERROR - download_util - download_to_tmp_path:61 - download_attempt_failed: attempt=1, error=404 Client Error: NOT FOUND for url: https://httpbin.org/status/404
2025-08-30 09:56:23 - visual_tool - ERROR - download_util - download_to_tmp_path:61 - download_attempt_failed: attempt=2, error=404 Client Error: NOT FOUND for url: https://httpbin.org/status/404
2025-08-30 09:56:25 - visual_tool - ERROR - download_util - download_to_tmp_path:61 - download_attempt_failed: attempt=3, error=404 Client Error: NOT FOUND for url: https://httpbin.org/status/404
2025-08-30 09:56:25 - visual_tool - ERROR - download_util - download_to_tmp_path:67 - download_failed: attempts=3, error=404 Client Error: NOT FOUND for url: https://httpbin.org/status/404
Traceback (most recent call last):
  File "/Users/<USER>/code/baidu/dataeng/visual_tools/util/download_util.py", line 52, in download_to_tmp_path
    resp.raise_for_status()
  File "/Users/<USER>/.local/share/mise/installs/python/3.12.11/lib/python3.12/site-packages/requests/models.py", line 1021, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 404 Client Error: NOT FOUND for url: https://httpbin.org/status/404
2025-08-30 12:36:13 - visual_tool - ERROR - download_util - download_to_tmp_path:66 - [TraceID: 1756528571908_6ebd12ee] download_attempt_failed: attempt=1, error=('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-08-30 12:36:13 - visual_tool - ERROR - download_util - download_to_tmp_path:66 - [TraceID: 1756528571908_6ebd12ee] download_attempt_failed: attempt=2, error=('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-08-30 12:36:15 - visual_tool - ERROR - download_util - download_to_tmp_path:66 - [TraceID: 1756528571908_6ebd12ee] download_attempt_failed: attempt=3, error=('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-08-30 12:36:15 - visual_tool - ERROR - download_util - download_to_tmp_path:74 - [TraceID: 1756528571908_6ebd12ee] download_failed: attempts=3, error=('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
2025-08-30 12:36:22 - visual_tool - ERROR - processor - apply_video_tool:142 - [TraceID: 1756528571908_6ebd12ee] tmp_cleanup_failed, path=failed to download http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerJoyrides.mp4, error=unlink: path should be string, bytes or os.PathLike, not ValueError
2025-08-30 12:36:22 - visual_tool - ERROR - trace_util - log_request_response:155 - [TraceID: 1756528571908_6ebd12ee] 请求处理失败: expected str, bytes or os.PathLike object, not ValueError
2025-08-30 12:38:17 - visual_tool - ERROR - trace_util - log_request_response:155 - [TraceID: 1756528695045_fda6e5b0] 请求处理失败: 'VideoFileClip' object has no attribute 'subclip'
2025-08-30 12:41:01 - visual_tool - ERROR - upload_util - upload_file_to_bos:60 - [TraceID: 1756528845625_b167b77f] upload_failed
2025-08-30 12:41:01 - visual_tool - ERROR - trace_util - log_request_response:155 - [TraceID: 1756528845625_b167b77f] 请求处理失败: Unable to execute HTTP request. Retried 3 times. All trace backs:
>>>>Traceback (most recent call last):
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 210, in send_request
>>>>    http_response = _send_http_request(
>>>>                    ^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 101, in _send_http_request
>>>>    conn.send(buf)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/http/client.py", line 1019, in send
>>>>    self.sock.sendall(data)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1273, in sendall
>>>>    v = self.send(byte_view[count:])
>>>>        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1242, in send
>>>>    return self._sslobj.write(data)
>>>>           ^^^^^^^^^^^^^^^^^^^^^^^^
>>>>TimeoutError: The write operation timed out
>>>>Traceback (most recent call last):
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 210, in send_request
>>>>    http_response = _send_http_request(
>>>>                    ^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 101, in _send_http_request
>>>>    conn.send(buf)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/http/client.py", line 1019, in send
>>>>    self.sock.sendall(data)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1273, in sendall
>>>>    v = self.send(byte_view[count:])
>>>>        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1242, in send
>>>>    return self._sslobj.write(data)
>>>>           ^^^^^^^^^^^^^^^^^^^^^^^^
>>>>TimeoutError: The write operation timed out
>>>>Traceback (most recent call last):
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 210, in send_request
>>>>    http_response = _send_http_request(
>>>>                    ^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 101, in _send_http_request
>>>>    conn.send(buf)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/http/client.py", line 1019, in send
>>>>    self.sock.sendall(data)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1273, in sendall
>>>>    v = self.send(byte_view[count:])
>>>>        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1242, in send
>>>>    return self._sslobj.write(data)
>>>>           ^^^^^^^^^^^^^^^^^^^^^^^^
>>>>TimeoutError: The write operation timed out
>>>>Traceback (most recent call last):
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 210, in send_request
>>>>    http_response = _send_http_request(
>>>>                    ^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 101, in _send_http_request
>>>>    conn.send(buf)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/http/client.py", line 1019, in send
>>>>    self.sock.sendall(data)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1273, in sendall
>>>>    v = self.send(byte_view[count:])
>>>>        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1242, in send
>>>>    return self._sslobj.write(data)
>>>>           ^^^^^^^^^^^^^^^^^^^^^^^^
>>>>TimeoutError: The write operation timed out
2025-08-30 12:44:34 - visual_tool - ERROR - upload_util - upload_file_to_bos:60 - [TraceID: 1756529060890_d0832573] upload_failed
2025-08-30 12:44:34 - visual_tool - ERROR - trace_util - log_request_response:155 - [TraceID: 1756529060890_d0832573] 请求处理失败: Unable to execute HTTP request. Retried 3 times. All trace backs:
>>>>Traceback (most recent call last):
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 210, in send_request
>>>>    http_response = _send_http_request(
>>>>                    ^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 101, in _send_http_request
>>>>    conn.send(buf)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/http/client.py", line 1019, in send
>>>>    self.sock.sendall(data)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1273, in sendall
>>>>    v = self.send(byte_view[count:])
>>>>        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1242, in send
>>>>    return self._sslobj.write(data)
>>>>           ^^^^^^^^^^^^^^^^^^^^^^^^
>>>>TimeoutError: The write operation timed out
>>>>Traceback (most recent call last):
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 210, in send_request
>>>>    http_response = _send_http_request(
>>>>                    ^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 101, in _send_http_request
>>>>    conn.send(buf)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/http/client.py", line 1019, in send
>>>>    self.sock.sendall(data)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1273, in sendall
>>>>    v = self.send(byte_view[count:])
>>>>        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1242, in send
>>>>    return self._sslobj.write(data)
>>>>           ^^^^^^^^^^^^^^^^^^^^^^^^
>>>>TimeoutError: The write operation timed out
>>>>Traceback (most recent call last):
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 210, in send_request
>>>>    http_response = _send_http_request(
>>>>                    ^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 101, in _send_http_request
>>>>    conn.send(buf)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/http/client.py", line 1019, in send
>>>>    self.sock.sendall(data)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1273, in sendall
>>>>    v = self.send(byte_view[count:])
>>>>        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1242, in send
>>>>    return self._sslobj.write(data)
>>>>           ^^^^^^^^^^^^^^^^^^^^^^^^
>>>>TimeoutError: The write operation timed out
>>>>Traceback (most recent call last):
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 210, in send_request
>>>>    http_response = _send_http_request(
>>>>                    ^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 101, in _send_http_request
>>>>    conn.send(buf)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/http/client.py", line 1019, in send
>>>>    self.sock.sendall(data)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1273, in sendall
>>>>    v = self.send(byte_view[count:])
>>>>        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1242, in send
>>>>    return self._sslobj.write(data)
>>>>           ^^^^^^^^^^^^^^^^^^^^^^^^
>>>>TimeoutError: The write operation timed out
2025-08-30 12:50:47 - visual_tool - ERROR - upload_util - upload_file_to_bos:60 - [TraceID: 1756529433269_44796be7] upload_failed
2025-08-30 12:50:47 - visual_tool - ERROR - trace_util - log_request_response:155 - [TraceID: 1756529433269_44796be7] 请求处理失败: Unable to execute HTTP request. Retried 3 times. All trace backs:
>>>>Traceback (most recent call last):
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 210, in send_request
>>>>    http_response = _send_http_request(
>>>>                    ^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 101, in _send_http_request
>>>>    conn.send(buf)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/http/client.py", line 1019, in send
>>>>    self.sock.sendall(data)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1273, in sendall
>>>>    v = self.send(byte_view[count:])
>>>>        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1242, in send
>>>>    return self._sslobj.write(data)
>>>>           ^^^^^^^^^^^^^^^^^^^^^^^^
>>>>TimeoutError: The write operation timed out
>>>>Traceback (most recent call last):
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 210, in send_request
>>>>    http_response = _send_http_request(
>>>>                    ^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 101, in _send_http_request
>>>>    conn.send(buf)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/http/client.py", line 1019, in send
>>>>    self.sock.sendall(data)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1273, in sendall
>>>>    v = self.send(byte_view[count:])
>>>>        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1242, in send
>>>>    return self._sslobj.write(data)
>>>>           ^^^^^^^^^^^^^^^^^^^^^^^^
>>>>TimeoutError: The write operation timed out
>>>>Traceback (most recent call last):
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 210, in send_request
>>>>    http_response = _send_http_request(
>>>>                    ^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 101, in _send_http_request
>>>>    conn.send(buf)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/http/client.py", line 1019, in send
>>>>    self.sock.sendall(data)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1273, in sendall
>>>>    v = self.send(byte_view[count:])
>>>>        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1242, in send
>>>>    return self._sslobj.write(data)
>>>>           ^^^^^^^^^^^^^^^^^^^^^^^^
>>>>TimeoutError: The write operation timed out
>>>>Traceback (most recent call last):
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 210, in send_request
>>>>    http_response = _send_http_request(
>>>>                    ^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 101, in _send_http_request
>>>>    conn.send(buf)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/http/client.py", line 1019, in send
>>>>    self.sock.sendall(data)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1273, in sendall
>>>>    v = self.send(byte_view[count:])
>>>>        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1242, in send
>>>>    return self._sslobj.write(data)
>>>>           ^^^^^^^^^^^^^^^^^^^^^^^^
>>>>TimeoutError: The write operation timed out
2025-08-30 12:53:45 - visual_tool - ERROR - upload_util - upload_file_to_bos:60 - [TraceID: 1756529611941_25ff9437] upload_failed
2025-08-30 12:53:45 - visual_tool - ERROR - trace_util - log_request_response:155 - [TraceID: 1756529611941_25ff9437] 请求处理失败: Unable to execute HTTP request. Retried 3 times. All trace backs:
>>>>Traceback (most recent call last):
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 210, in send_request
>>>>    http_response = _send_http_request(
>>>>                    ^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 101, in _send_http_request
>>>>    conn.send(buf)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/http/client.py", line 1019, in send
>>>>    self.sock.sendall(data)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1273, in sendall
>>>>    v = self.send(byte_view[count:])
>>>>        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1242, in send
>>>>    return self._sslobj.write(data)
>>>>           ^^^^^^^^^^^^^^^^^^^^^^^^
>>>>TimeoutError: The write operation timed out
>>>>Traceback (most recent call last):
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 210, in send_request
>>>>    http_response = _send_http_request(
>>>>                    ^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 101, in _send_http_request
>>>>    conn.send(buf)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/http/client.py", line 1019, in send
>>>>    self.sock.sendall(data)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1273, in sendall
>>>>    v = self.send(byte_view[count:])
>>>>        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1242, in send
>>>>    return self._sslobj.write(data)
>>>>           ^^^^^^^^^^^^^^^^^^^^^^^^
>>>>TimeoutError: The write operation timed out
>>>>Traceback (most recent call last):
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 210, in send_request
>>>>    http_response = _send_http_request(
>>>>                    ^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 101, in _send_http_request
>>>>    conn.send(buf)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/http/client.py", line 1019, in send
>>>>    self.sock.sendall(data)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1273, in sendall
>>>>    v = self.send(byte_view[count:])
>>>>        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1242, in send
>>>>    return self._sslobj.write(data)
>>>>           ^^^^^^^^^^^^^^^^^^^^^^^^
>>>>TimeoutError: The write operation timed out
>>>>Traceback (most recent call last):
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 210, in send_request
>>>>    http_response = _send_http_request(
>>>>                    ^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 101, in _send_http_request
>>>>    conn.send(buf)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/http/client.py", line 1019, in send
>>>>    self.sock.sendall(data)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1273, in sendall
>>>>    v = self.send(byte_view[count:])
>>>>        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1242, in send
>>>>    return self._sslobj.write(data)
>>>>           ^^^^^^^^^^^^^^^^^^^^^^^^
>>>>TimeoutError: The write operation timed out
2025-08-30 13:13:36 - visual_tool - ERROR - upload_util - upload_file_to_bos:60 - [TraceID: 1756530803168_f4fc984f] upload_failed
2025-08-30 13:13:36 - visual_tool - ERROR - trace_util - log_request_response:155 - [TraceID: 1756530803168_f4fc984f] 请求处理失败: Unable to execute HTTP request. Retried 3 times. All trace backs:
>>>>Traceback (most recent call last):
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 210, in send_request
>>>>    http_response = _send_http_request(
>>>>                    ^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 101, in _send_http_request
>>>>    conn.send(buf)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/http/client.py", line 1019, in send
>>>>    self.sock.sendall(data)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1273, in sendall
>>>>    v = self.send(byte_view[count:])
>>>>        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1242, in send
>>>>    return self._sslobj.write(data)
>>>>           ^^^^^^^^^^^^^^^^^^^^^^^^
>>>>TimeoutError: The write operation timed out
>>>>Traceback (most recent call last):
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 210, in send_request
>>>>    http_response = _send_http_request(
>>>>                    ^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 101, in _send_http_request
>>>>    conn.send(buf)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/http/client.py", line 1019, in send
>>>>    self.sock.sendall(data)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1273, in sendall
>>>>    v = self.send(byte_view[count:])
>>>>        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1242, in send
>>>>    return self._sslobj.write(data)
>>>>           ^^^^^^^^^^^^^^^^^^^^^^^^
>>>>TimeoutError: The write operation timed out
>>>>Traceback (most recent call last):
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 210, in send_request
>>>>    http_response = _send_http_request(
>>>>                    ^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 101, in _send_http_request
>>>>    conn.send(buf)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/http/client.py", line 1019, in send
>>>>    self.sock.sendall(data)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1273, in sendall
>>>>    v = self.send(byte_view[count:])
>>>>        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1242, in send
>>>>    return self._sslobj.write(data)
>>>>           ^^^^^^^^^^^^^^^^^^^^^^^^
>>>>TimeoutError: The write operation timed out
>>>>Traceback (most recent call last):
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 210, in send_request
>>>>    http_response = _send_http_request(
>>>>                    ^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/code/baidu/dataeng/visual_tools/.venv/lib/python3.11/site-packages/baidubce/http/bce_http_client.py", line 101, in _send_http_request
>>>>    conn.send(buf)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/http/client.py", line 1019, in send
>>>>    self.sock.sendall(data)
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1273, in sendall
>>>>    v = self.send(byte_view[count:])
>>>>        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
>>>>  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/ssl.py", line 1242, in send
>>>>    return self._sslobj.write(data)
>>>>           ^^^^^^^^^^^^^^^^^^^^^^^^
>>>>TimeoutError: The write operation timed out
2025-08-30 13:24:50 - visual_tool - ERROR - trace_util - log_request_response:155 - [TraceID: 1756531489283_e09592c6] 请求处理失败: end_time (30.00) should be smaller or equal to the clip's duration (15.05).
2025-08-30 13:25:02 - visual_tool - ERROR - trace_util - log_request_response:155 - [TraceID: 1756531501833_312e1170] 请求处理失败: end_time (15.50) should be smaller or equal to the clip's duration (15.05).
2025-09-03 18:34:08 - visual_tool - ERROR - trace_util - log_request_response:155 - [TraceID: 1756895648382_d9709923] 请求处理失败: Missing required argument 'start_time' for video_clip_tool
2025-09-03 19:04:50 - visual_tool - ERROR - trace_util - log_request_response:155 - [TraceID: 1756897468766_410e6ed2] 请求处理失败: either extra_params.image_url or extra_params.image_data is required for image tools
2025-09-03 19:05:12 - visual_tool - ERROR - trace_util - log_request_response:155 - [TraceID: 1756897512213_57416134] 请求处理失败: Missing required argument 'start_time' for video_clip_tool
2025-09-03 19:05:25 - visual_tool - ERROR - trace_util - log_request_response:155 - [TraceID: 1756897524733_0a4c1e68] 请求处理失败: Missing required argument 'end_time' for video_clip_tool
2025-09-03 19:05:42 - visual_tool - ERROR - trace_util - log_request_response:155 - [TraceID: 1756897541675_9f9d3740] 请求处理失败: start_time must be less than end_time
2025-09-03 19:05:45 - visual_tool - ERROR - trace_util - log_request_response:155 - [TraceID: 1756897544642_e4371920] 请求处理失败: start_time must be less than end_time
2025-09-03 19:07:24 - visual_tool - ERROR - trace_util - log_request_response:155 - [TraceID: 1756897643786_94bc4b94] 请求处理失败: Missing required argument 'channels' for image_calchist_tool
