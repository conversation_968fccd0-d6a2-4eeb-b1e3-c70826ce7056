# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Visual Tools API service that provides image and video processing capabilities through a FastAPI-based web service. The project supports various image manipulation tools (crop, rotate, resize, filters, etc.) and video processing tools (clip, frame extraction).

## Development Commands

### Environment Setup
```bash
# Install dependencies using uv (recommended)
uv pip install -r requirements.txt --index-url https://pip.baidu-int.com/simple/ --trusted-host pip.baidu.com

# Install development dependencies
uv pip install -r requirements-dev.txt --index-url https://pip.baidu-int.com/simple/ --trusted-host pip.baidu.com
```

### Running the Application
```bash
# Run with default settings (port 8080, 8 workers)
python src/app.py

# Run with custom settings
main=8080 workers=8 timeout=60 python src/app.py

# Run using uvicorn directly
uvicorn src.app:app --host 0.0.0.0 --port 8080 --workers 8 --timeout-keep-alive 60
```

### Building and Deployment
```bash
# Build the project (copies src to output directory)
sh build.sh

# Run tests (if available)
pytest

# Docker build
docker build -t visual-tools .
```

## Architecture Overview

### Core Components

1. **FastAPI Application** (`src/app.py`): Main entry point with API routing, middleware setup, and request handling
2. **Image Processing** (`src/image_tool/processor.py`): Handles all image manipulation operations
3. **Video Processing** (`src/video_tool/processor.py`): Handles video clipping and frame extraction
4. **Utility Modules** (`src/util/`): Shared utilities for logging, file operations, BOS integration, etc.

### Key Architecture Patterns

- **Tool-Based Processing**: The API routes requests to specific tools based on the `name` parameter in the request payload
- **Static Functions**: All processing logic uses static functions without instance state
- **Temporary File Management**: Downloads and processing use temporary files with automatic cleanup
- **BOS Integration**: All processed files are uploaded to BOS (Baidu Object Storage) and return public URLs

### Request Flow

1. Request comes to `/visual_tools/v1/visual_tool` endpoint
2. Based on `name` parameter, routes to either image or video processor
3. Downloads input file (image/video) from URL or accepts base64 data
4. Processes the file using the specified tool
5. Uploads result to BOS and returns public URL
6. Cleans up temporary files

### Supported Tools

**Image Tools** (defined in `SUPPORTED_IMAGE_TOOLS`):
- Basic operations: crop, rotate, resize, flip
- Enhancements: brightness, contrast, color, sharpness
- Analysis: histogram, edge detection, smoothing, contours, face detection

**Video Tools** (defined in `SUPPORTED_VIDEO_TOOLS`):
- Video clipping by time range
- Frame extraction from specific timestamps

### Utility Modules

- `logging_util.py`: Logging configuration and setup
- `download_util.py`: URL downloading with retry logic
- `upload_util.py`: BOS file upload with MD5 hashing
- `bos_util.py`: BOS client configuration
- `trace_util.py`: Request/response tracing
- `exceptions.py`: Custom exception classes
- `middleware.py`: FastAPI middleware for logging, tracing, and metrics
- `proc_stats.py`: Process statistics monitoring

### Configuration

- **Logging**: Uses YAML configuration file (`logging_config.yaml`) with fallback to default
- **Dependencies**: Uses `uv` for package management with Baidu internal package index
- **Docker**: Multi-stage build with Python 3.10, ffmpeg integration for video processing
- **Environment**: Configurable via environment variables for port, workers, timeout

### Error Handling

- Custom `ParameterException` for invalid input parameters
- Standardized error response format with error codes
- Comprehensive logging and tracing for debugging
- Automatic temporary file cleanup in finally blocks

### Testing and Quality

- Uses pytest for testing (test files should be placed appropriately)
- Type hints throughout the codebase
- Comprehensive error handling and logging
- Prometheus metrics integration for monitoring