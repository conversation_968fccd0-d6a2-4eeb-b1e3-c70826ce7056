Global:
  version: "2.0"
  group_email: <EMAIL>

Default:
  profile: [publish]

Profiles:
  - profile:
    name: dev
    mode: AGENT
    environment:
      image: DECK_STD_CENTOS7
      tools:
        - python: 3.10.10
    build:
      command: sh build.sh
    artifacts:
      release: true

  - profile:
    name: publish
    mode: AGENT
    environment:
      image: DECK_STD_CENTOS7
      tools:
        - python: 3.10.10
    build:
      command: sh build.sh
    artifacts:
      release: true